openapi: 3.0.0
info:
    title: Expanded spec
    version: v0.1.0
servers:
    -   url: http://localhost:3000
paths:
    /refer/{token}:
        get:
            summary: Get Email Refer Throttle By Token
            operationId: get-refer-token
            parameters:
                -   name: token
                    in: path
                    required: true
                    style: simple
                    explode: false
                    schema:
                        type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/PublicReferEmailThrottle'
                "400":
                    description: Bad Request
                "500":
                    description: Internal Server Error
        post:
            summary: Post an email friends refer
            operationId: post-refer-token
            parameters:
                -   name: token
                    in: path
                    required: true
                    style: simple
                    explode: false
                    schema:
                        type: string
            requestBody:
                content:
                    multipart/form-data:
                        schema:
                            $ref: '#/components/schemas/PublicReferral'
            responses:
                "200":
                    description: OK
                "400":
                    description: Bad Request
                "500":
                    description: Internal Server Error
    /v1/appointments:
        get:
            operationId: get-v1-appointments
            parameters:
                -   name: provider_id
                    in: query
                    description: Legacy ID of provider
                    required: true
                    style: form
                    explode: true
                    schema:
                        type: integer
                        format: int64
                -   name: consent_id
                    in: query
                    description: Consent ID
                    required: true
                    style: form
                    explode: true
                    schema:
                        type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/AppointmentDetails'
                "400":
                    description: Bad Request
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Error'
                "401":
                    description: Unauthorized
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Error'
                "500":
                    description: Internal Server Error
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Error'
    /v1/appointments/patientstatus:
        post:
            description: Updates patient status for an appointment reminder with a given id, if status was not set yet. Returns Bad Request error if status was already set, Unauthorized on failed authentication and Not Found if no matching reminder exists.
            operationId: post-v1-appointments-patientstatus
            requestBody:
                content:
                    application/json:
                        schema:
                            type: object
                            properties:
                                records:
                                    $ref: '#/components/schemas/AppointmentReminderPatientStatusUpdate'
                                file:
                                    type: string
            responses:
                "200":
                    description: OK
                "400":
                    description: Bad Request
                "401":
                    description: Unauthorized
                "404":
                    description: Not Found
                "500":
                    description: Internal Server Error
    /v1/images/{imageID}:
        get:
            summary: Get image
            description: |-
                Get image by image ID.
                Both logged in users and validated share viewers can access.
                To retrieve image as PNG, set Accept header to image/png.
                To retrieve image as DICOM, set Accept header to application/dicom.
            operationId: get-image-byID
            parameters:
                -   name: Accept
                    in: header
                    description: Tell the API what format to return the image
                    required: true
                    style: simple
                    explode: false
                    schema:
                        type: string
                        enum:
                            - "application/dicom"
                            - "image/png"
                -   name: X-Image-Token
                    in: header
                    description: Authentication token for image access
                    style: simple
                    explode: false
                    schema:
                        type: string
                -   name: imageID
                    in: path
                    required: true
                    style: simple
                    explode: false
                    schema:
                        type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/dicom:
                            schema:
                                type: string
                                format: binary
                        image/png:
                            schema:
                                type: string
                                format: binary
                "400":
                    description: Bad Request
                "401":
                    description: Unauthorized
                "403":
                    description: Forbidden
                "404":
                    description: Image with id <imageId> not found.
                "500":
                    description: Internal Server Error
            security:
                -   jwtBearer: []
    /v1/images/{imageid}/metadata:
        get:
            summary: Get image metadata
            description: Get image metadata
            operationId: get-image-metadata-byID
            parameters:
                -   name: imageid
                    in: path
                    required: true
                    style: simple
                    explode: false
                    schema:
                        type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ImageMetadata'
                "401":
                    description: Unauthorized
                "403":
                    description: Forbidden
                "404":
                    description: Image with id <imageID> not found
                "500":
                    description: Internal Server Error
            security:
                -   jwtBearer: []
    /v1/internal/patients/{patient_id}/phiprofiles:
        delete:
            summary: Delete associated phi_profiles for a patient_id
            operationId: delete-internal-patient-profiles
            parameters:
                -   name: patient_id
                    in: path
                    required: true
                    style: simple
                    explode: false
                    schema:
                        type: string
            responses:
                "200":
                    description: OK
                "401":
                    description: Invalid Signature
                "500":
                    description: Internal Server Error
            security:
                -   phSignature: []
    /v1/meddream/generate/{exam_uuid}:
        get:
            summary: Generate a token for MedDream
            parameters:
                -   name: exam_uuid
                    in: path
                    required: true
                    style: simple
                    explode: false
                    schema:
                        type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: string
                "401":
                    description: Unauthorized
                "404":
                    description: Not Found
                "500":
                    description: Internal Server Error
            security:
                -   jwtBearer: []
    /v1/physician_accounts:
        get:
            summary: Get Physician Account
            description: Returns physician account for given account_id
            operationId: get-physician-account
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/PhysicianAccount'
                "400":
                    description: Bad Request
                "401":
                    description: Unauthorized
                "404":
                    description: Not Found
                "500":
                    description: Internal Server Error
            security:
                -   jwtBearer: []
        post:
            summary: Create Physician Account
            description: Create a new Physician Account
            operationId: post-physician-accounts
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/RegisterData'
            responses:
                "200":
                    description: OK. Returns ID of newly created physician account. Sends the 'Verify Email' email for physician accounts.
                    content:
                        application/json:
                            schema:
                                type: string
                "400":
                    description: Bad Request
                "401":
                    description: Unauthorized
                "406":
                    description: Not Accceptable. Bad Password provided.
                "500":
                    description: Internal Server Error
        patch:
            summary: Patch Physician Account Password
            description: Update an physician account's password based on recovery token and code and new password.
            operationId: patch-physician-accounts
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/PasswordResetInfo'
            responses:
                "200":
                    description: OK
                "400":
                    description: Bad Request
                "401":
                    description: Unauthorized
                "404":
                    description: Not Found
                "406":
                    description: Not Accceptable. Bad Password provided.
                "500":
                    description: Internal Server Error
            security:
                -   jwtBearer: []
    /v1/physician_accounts/login:
        post:
            summary: Login
            description: Login a physician with email and password. Returns jwt and refresh token on success, error response otherwise.
            operationId: post-physician-accounts-login
            requestBody:
                content:
                    application/json:
                        schema:
                            type: object
                            properties:
                                email:
                                    type: string
                                password:
                                    type: string
                            required:
                                - email
                                - password
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                description: token
                                type: object
                                properties:
                                    token:
                                        type: string
                                    uuid:
                                        type: string
                                    betas:
                                        type: array
                                        items:
                                            type: string
                "400":
                    description: Bad Request
                "401":
                    description: Unauthorized
                "403":
                    description: Forbidden (account locked)
                "500":
                    description: Internal Server Error
    /v1/physician_accounts/logout:
        post:
            summary: Logout
            description: Logout a physician by blacklisting the given auth token.
            operationId: post-physician-accounts-logout
            responses:
                "200":
                    description: OK
                "500":
                    description: Internal Server Error
            security:
                -   jwtBearer: []
    /v1/physician_accounts/patients:
        get:
            summary: Get Physician Patients
            description: Returns the patients of a physician
            operationId: get-physician-account-patients
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: array
                                items:
                                    $ref: '#/components/schemas/PhysicianPatientSummary'
                "401":
                    description: Unauthorized
                "500":
                    description: Internal Server Error
            security:
                -   jwtBearer: []
    /v1/physician_accounts/patients/{patient_id}:
        get:
            summary: Get Physician Patients Exams
            description: Returns the exams of a patient of a physician
            operationId: get-physician-account-patients-exams
            parameters:
                -   name: patient_id
                    in: path
                    required: true
                    style: simple
                    explode: false
                    schema:
                        type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    hrs:
                                        type: array
                                        items:
                                            $ref: '#/components/schemas/Record'
                                    exams:
                                        type: array
                                        items:
                                            $ref: '#/components/schemas/Exam'
                                    examShareInfoMap:
                                        type: object
                                        properties:
                                            "":
                                                $ref: '#/components/schemas/PhysicianShareInfo'
                                        additionalProperties:
                                            type: string
                                    hrShareInfoMap:
                                        type: object
                                        properties:
                                            "":
                                                $ref: '#/components/schemas/PhysicianShareInfo'
                "401":
                    description: Unauthorized
                "500":
                    description: Internal Server Error
            security:
                -   jwtBearer: []
    /v1/physician_accounts/physicians/{physician_id}/licenses:
        post:
            summary: Add Physician License to Physician
            description: Add a new Physician License to an existing Physician.
            operationId: post-physician-license
            parameters:
                -   name: physician_id
                    in: path
                    required: true
                    style: simple
                    explode: false
                    schema:
                        type: string
            requestBody:
                $ref: '#/components/requestBodies/PhysicianLicenseRequest'
            responses:
                "200":
                    description: OK. Returns ID of newly created physician license.
                    content:
                        application/json:
                            schema:
                                type: string
                "400":
                    description: Bad Request
                "401":
                    description: Unauthorized
                "404":
                    description: Not Found
                "500":
                    description: Internal Server Error
            security:
                -   jwtBearer: []
    /v1/physician_accounts/physicians/{physician_id}/verify:
        post:
            summary: Verify Physician Notification Method
            description: Verify a fax or email for Physician using a verification code.
            operationId: verify-physician-notification-method
            parameters:
                -   name: physician_id
                    in: path
                    required: true
                    style: simple
                    explode: false
                    schema:
                        type: string
            requestBody:
                content:
                    application/json:
                        schema:
                            type: object
                            properties:
                                notification_method:
                                    $ref: '#/components/schemas/NotificationMethod'
                                value:
                                    type: string
                            required:
                                - notification_method
                                - value
            responses:
                "200":
                    description: OK.
                    content:
                        application/json:
                            schema:
                                type: string
                "400":
                    description: Bad Request
                "401":
                    description: Unauthorized
                "404":
                    description: Not Found
                "500":
                    description: Internal Server Error
            security:
                -   jwtBearer: []
    /v1/physician_accounts/reset:
        post:
            summary: Reset Physician Account Password
            description: |-
                Send password reset instructions to the email if physician account exists and password is set.
                Otherwise send a different email depending on the state of the account.
            operationId: post-physician-accounts-reset
            requestBody:
                content:
                    application/json:
                        schema:
                            type: string
                            format: email
            responses:
                "200":
                    description: OK
                "400":
                    description: Bad Request
                "401":
                    description: Unauthorized
                "404":
                    description: Not Found.
                "500":
                    description: Internal Server Error
    /v1/physician_accounts/search:
        post:
            summary: Search Shares for Physician Account
            description: |-
                Sends a request to an azure topic for record search based on given search criteria
                if physician has required permissions to query provider's system.
                Returns a queryId for fetching provider search results async.
            operationId: post-physician-accounts-search
            requestBody:
                $ref: '#/components/requestBodies/PhysicianSearchRequest'
            responses:
                "200":
                    description: OK
                    content:
                        text/plain:
                            schema:
                                type: string
                "400":
                    description: Bad Request
                "401":
                    description: Unauthorized
                "404":
                    description: Not Found.
                "500":
                    description: Internal Server Error
            security:
                -   jwtBearer: []
    /v1/physician_accounts/shares/{share_id}/extend:
        put:
            summary: Put Physician Extend Share
            description: Extend a physician's share's expiry
            operationId: put-physician-accounts-share-extend
            parameters:
                -   name: share_id
                    in: path
                    required: true
                    style: simple
                    explode: false
                    schema:
                        type: string
            responses:
                "200":
                    description: OK
                "401":
                    description: Unauthorized
                "500":
                    description: Internal Server Error
            security:
                -   jwtBearer: []
    /v1/physician_accounts/shares/{share_id}/token:
        get:
            summary: Get Physician E-Unity Token
            description: Creates and returns a eunity token for a share for physician account access
            operationId: get-physician-account-eunity-token
            parameters:
                -   name: share_id
                    in: path
                    required: true
                    style: simple
                    explode: false
                    schema:
                        type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: string
                "401":
                    description: Unauthorized
                "500":
                    description: Internal Server Error
            security:
                -   jwtBearer: []
    /v1/physician_accounts/shares/request:
        post:
            summary: Request Shares from a provider for a Physician Account
            description: |-
                Sends a request to an azure topic for access to a list of records
                if physician has required permissions to query provider's system.
            operationId: post-physician-accounts-request
            requestBody:
                $ref: '#/components/requestBodies/PhysicianRecordRequest'
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: array
                                items:
                                    $ref: '#/components/schemas/PhysicianRecordResponse'
                "400":
                    description: Bad Request
                "401":
                    description: Unauthorized
                "500":
                    description: Internal Server Error
            security:
                -   jwtBearer: []
    /v1/physician_accounts/studies:
        get:
            summary: Get physician studies with upload state
            description: Get list of record streaming studies with upload status that physician has access to
            operationId: get-physician-account-study-states
            responses:
                "200":
                    description: list of upload statuses for record streaming studies the physician has access to
                    content:
                        application/json:
                            schema:
                                type: array
                                items:
                                    $ref: '#/components/schemas/PhysicianRecordUploadStatus'
                "401":
                    description: Unauthorized
                "500":
                    description: Internal Server Error
            security:
                -   jwtBearer: []
    /v1/physician_accounts/studies/verify-access:
        post:
            summary: Posts a study access request
            description: Sends a request for accessing a study. Physician access is verified and an audit event for the access is logged if access was granted via record streaming. No audit logging is done for patient- or physician-initiated shares. Returns status code 200 if access was verified and, in case of record streaming studies, audit logging was successful.
            operationId: post-physician-account-study-access
            requestBody:
                $ref: '#/components/requestBodies/PhysicianAccessVerificationRequest'
            responses:
                "200":
                    description: list of upload statuses for record streaming studies the physician has access to
                "401":
                    description: Unauthorized
                "500":
                    description: Internal Server Error
            security:
                -   jwtBearer: []
    /v1/physician_accounts/verify:
        post:
            summary: Verify Physician Account Email
            description: Verify email after physician account creation.
            operationId: post-physician-accounts-verify
            requestBody:
                content:
                    application/json:
                        schema:
                            type: object
                            properties:
                                token:
                                    type: string
                                ip:
                                    type: string
            responses:
                "200":
                    description: OK
                "400":
                    description: Bad Request
                "401":
                    description: Unauthorized
                "404":
                    description: Not Found
                "500":
                    description: Internal Server Error
    /v1/promo/{code}:
        get:
            summary: Check promo code
            description: Check if a promo code is valid before submitting a transfer or request payment.
            operationId: get-v1-promo-code
            parameters:
                -   name: code
                    in: path
                    required: true
                    style: simple
                    explode: false
                    schema:
                        type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    valid:
                                        type: boolean
                                required:
                                    - valid
                "404":
                    description: Not Found
                "500":
                    description: Internal Server Error
            deprecated: true
    /v1/providers:
        get:
            summary: Get providers
            description: Get list of participating providers, filter by searchTerm query parameter.
            operationId: get-providers
            parameters:
                -   name: searchTerm
                    in: query
                    style: form
                    explode: true
                    schema:
                        type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: array
                                items:
                                    $ref: '#/components/schemas/Provider'
                "500":
                    description: Internal Server Error
    /v1/providers/{providerId}:
        get:
            summary: Get a provider
            description: Get a single provider
            operationId: get-provider-byId
            parameters:
                -   name: providerId
                    in: path
                    required: true
                    style: simple
                    explode: false
                    schema:
                        type: integer
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Provider'
                "404":
                    description: Not Found
                "500":
                    description: Internal Server Error
    /v1/providers/{providerId}/formConfig:
        get:
            summary: Get request form config
            description: Get request form field options to build a request form for this provider.
            operationId: get-provider-formConfig
            parameters:
                -   name: providerId
                    in: path
                    required: true
                    style: simple
                    explode: false
                    schema:
                        type: integer
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/RequestFormConfig'
                "404":
                    description: Provider with id not found.
                "500":
                    description: Internal Server Error
    /v1/providers/consents/{consentId}/type:
        get:
            summary: Get consent type
            description: Get consent type (verified or unverified)
            operationId: get-providers-consents-consentid-type
            parameters:
                -   name: consentId
                    in: path
                    required: true
                    style: simple
                    explode: false
                    schema:
                        type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ConsentType'
    /v1/providers/consents/{consentid}:
        get:
            summary: Get consent form data
            description: Get the data for the consent form for a given consent id
            operationId: get-providers-consents-consentid
            parameters:
                -   name: consentid
                    in: path
                    required: true
                    style: simple
                    explode: false
                    schema:
                        type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ConsentFormData'
        post:
            summary: Post consent
            description: 'Post consent form details '
            operationId: post-providers-consents-consentid
            parameters:
                -   name: consentid
                    in: path
                    required: true
                    style: simple
                    explode: false
                    schema:
                        type: string
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/Consent'
            responses:
                "200":
                    description: OK
                    content:
                        application/pdf:
                            schema:
                                type: string
                                format: binary
                "400":
                    description: Bad Request
                "404":
                    description: Not Found
                "500":
                    description: Internal Server Error
    /v1/providers/consents/{consentid}/email-verification:
        post:
            summary: Post consent email verification
            description: Post consent email verification
            operationId: post-providers-consents-email-verification
            parameters:
                -   name: consentid
                    in: path
                    required: true
                    style: simple
                    explode: false
                    schema:
                        type: string
            requestBody:
                content:
                    application/json:
                        schema:
                            description: email
                            type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ConsentEmailVerification'
                "400":
                    description: Bad Request
                "404":
                    description: Not Found
                "500":
                    description: Internal Server Error
    /v1/providers/consents/{consentid}/unverified:
        get:
            summary: Get consent form data
            description: Get consent form data for a given consent id without date of birth verification required
            operationId: get-providers-consents-consentid-unverified
            parameters:
                -   name: consentid
                    in: path
                    required: true
                    style: simple
                    explode: false
                    schema:
                        type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ConsentFormData'
        post:
            summary: Post consent
            description: 'Post consent form details '
            operationId: post-providers-consents-consentid-unverified
            parameters:
                -   name: consentid
                    in: path
                    required: true
                    style: simple
                    explode: false
                    schema:
                        type: string
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/Consent'
            responses:
                "200":
                    description: OK
                    content:
                        application/pdf:
                            schema:
                                type: string
                                format: binary
                "400":
                    description: Bad Request
                "404":
                    description: Not Found
                "500":
                    description: Internal Server Error
    /v1/providers/providerConfig:
        get:
            summary: Get provider level configs.
            description: Get provider level settings to enable/disable various settings in the frontend.
            operationId: get-provider-config
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ProviderConfig'
                "500":
                    description: Internal Server Error
    /v1/records/upload-status:
        get:
            summary: Get record upload status list for a patient. Currently only considers studies that have been uploaded via record streaming.
            description: |-
                Get a list of study upload status objects for all studies
                that have been uploaded via record streaming
                that a patient has access to.
                Upload status contains study metadata, provider data and the upload percentage of the study.
            parameters:
                -   name: account_id
                    in: query
                    required: true
                    style: form
                    explode: true
                    schema:
                        type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: array
                                items:
                                    $ref: '#/components/schemas/RecordUploadStatus'
                "401":
                    description: Unauthorized
                "500":
                    description: Internal Server Error
    /v1/reports:
        get:
            summary: Get unassociated reports
            description: Get reports not associated with any exam
            operationId: get-reports
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: array
                                items:
                                    $ref: '#/components/schemas/Report'
                "401":
                    description: Unauthorized
                "500":
                    description: Internal Server Error
            security:
                -   jwtBearer: []
    /v1/reports/{id}/taggedhtml:
        get:
            summary: Get tagged report with definitions
            description: Returns the report identified by id in html, with defined terms tagged with the css class phdefinition. Also returns a dictionary of terms and definitions
            operationId: get-v1-reports-id-taggedhtml
            parameters:
                -   name: id
                    in: path
                    required: true
                    style: simple
                    explode: false
                    schema:
                        type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/TaggedHTML'
                "401":
                    description: Unauthorized
                "404":
                    description: Report not found or account has no permission to access the report
                "500":
                    description: Internal Server Error
            security:
                -   jwtBearer: []
    /v1/reports/{reportId}:
        get:
            summary: Get report
            description: Get report by report ID. When Accept header set to application/pdf it will be returned as a PDF. When set to image/png, returned as image.
            operationId: get-report-byId
            parameters:
                -   name: Accept
                    in: header
                    description: Tell the API what format to return the report
                    required: true
                    style: simple
                    explode: false
                    schema:
                        type: string
                        enum:
                            - "application/pdf"
                            - "image/png"
                            - "text/html"
                -   name: reportId
                    in: path
                    required: true
                    style: simple
                    explode: false
                    schema:
                        type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/pdf:
                            schema:
                                type: string
                                format: binary
                "400":
                    description: Bad Request
                "401":
                    description: Unauthorized
                "404":
                    description: Report with id <reportId> not found or has no permission to access report
                "500":
                    description: Internal Server Error
            security:
                -   jwtBearer: []
    /v1/reports/{reportId}/insights/followup:
        get:
            summary: Get v2 follow up
            description: |-
                Grabs PDF report from storage, parse into raw text, and write to `reportinsightsdata` storage container \
                Then, call report insights to generate followup information
            operationId: get-v1-reports-reportId-insights-followup
            parameters:
                -   name: reportId
                    in: path
                    required: true
                    style: simple
                    explode: false
                    schema:
                        type: string
            responses:
                "200":
                    description: OK
                "401":
                    description: Unauthorized
                "404":
                    description: Report not found or account has no permission to access the report
                "500":
                    description: Internal Server Error
    /v1/reports/{reportId}/insights/questions:
        get:
            summary: Get questions for your doctor
            description: |-
                Grabs PDF report from storage, parse into raw text, and write to `reportinsightsdata` storage container \
                Then, call report insights to generate questions for your doctor
            operationId: get-reports-reportId-insights-questions
            parameters:
                -   name: reportId
                    in: path
                    required: true
                    style: simple
                    explode: false
                    schema:
                        type: string
            responses:
                "200":
                    description: OK
                "401":
                    description: Unauthorized
                "404":
                    description: Report not found or account has no permission to access the report
                "500":
                    description: Internal Server Error
    /v1/reports/{reportId}/metadata:
        get:
            summary: Get report metadata
            description: Get report metadata
            operationId: get-reports-reportId-metadata
            parameters:
                -   name: reportId
                    in: path
                    required: true
                    style: simple
                    explode: false
                    schema:
                        type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Report'
                "401":
                    description: Unauthorized
                "404":
                    description: Not Found
                "500":
                    description: Internal Server Error
            security:
                -   jwtBearer: []
                -   phSignature: []
    /v1/reports/{reportId}/organviz:
        post:
            summary: Request for organ visualization of body parts
            description: |-
                Choose the image slice from the exam (or use the image if object_id is supplied in the request), download from \
                prod image blob storage and upload to reportinsightsdata blob storage for reportinsights to use for inferencing \
                call reportinsights
            operationId: get-reports-reportId-organ-visualiation
            parameters:
                -   name: reportId
                    in: path
                    required: true
                    style: simple
                    explode: false
                    schema:
                        type: string
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/OrganVisualizationRequest'
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    organs:
                                        type: array
                                        items:
                                            $ref: '#/components/schemas/OrganVisualization'
                "401":
                    description: Unauthorized
                "404":
                    description: Report not found or account has no permission to access the report
                "500":
                    description: Internal Server Error
    /v1/reports/{reportId}/views:
        post:
            summary: Post report views
            description: Post that report has been viewed.
            operationId: post-report-views
            parameters:
                -   name: reportId
                    in: path
                    required: true
                    style: simple
                    explode: false
                    schema:
                        type: string
            responses:
                "200":
                    description: OK
                "401":
                    description: Unauthorized
                "500":
                    description: Internal Server Error
    /v1/requests:
        get:
            summary: Get pending requests
            description: Get requests for the authenticated user
            operationId: get-requests
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: array
                                items:
                                    $ref: '#/components/schemas/PendingRequest'
                "401":
                    description: Unauthorized
                "403":
                    description: Forbidden
                "404":
                    description: Not Found
                "500":
                    description: Internal Server Error
            security:
                -   jwtBearer: []
        post:
            summary: Create a new request
            description: |-
                Initiate a new request.
                Returns PDF form including signature.
            operationId: post-requests
            requestBody:
                content:
                    multipart/form-data:
                        schema:
                            $ref: '#/components/schemas/NewRequest'
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                description: Signed request form
                                type: object
                                properties:
                                    requestId:
                                        type: string
                                    consentPdf:
                                        type: string
                                        format: binary
                                    registerPin:
                                        type: string
                                        deprecated: true
                "400":
                    description: Bad Request
                "402":
                    description: Payment Required
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/PaymentError'
                "500":
                    description: Internal Server Error
    /v1/requests/{id}/cancel:
        patch:
            summary: Cancel request
            description: Updates request status to VOID which cancels the request.
            operationId: patch-requests-id-cancel
            parameters:
                -   name: id
                    in: path
                    required: true
                    style: simple
                    explode: false
                    schema:
                        type: string
            responses:
                "200":
                    description: OK
                "400":
                    description: Bad Request
                "401":
                    description: Unauthorized
                "404":
                    description: Not Found
                "500":
                    description: Internal Server Error
            security:
                -   jwtBearer: []
    /v1/requests/{id}/rejectVerify:
        post:
            summary: Send patient verified email
            description: Send an email to PX after a rejected request has had details verified by the patient
            operationId: post-requests-id-rejectVerify
            parameters:
                -   name: id
                    in: path
                    required: true
                    style: simple
                    explode: false
                    schema:
                        type: string
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/RejectVerifyDetails'
            responses:
                "200":
                    description: OK
            security:
                -   jwtBearer: []
    /v1/requests/{id}/resubmit:
        put:
            summary: Resubmit request
            description: Update details of request and resubmit. Same request body and response body of POST /requests except there's no payment token in the request.
            operationId: post-requests-id-resubmit
            parameters:
                -   name: id
                    in: path
                    required: true
                    style: simple
                    explode: false
                    schema:
                        type: string
            requestBody:
                content:
                    multipart/form-data:
                        schema:
                            type: object
                            properties:
                                requestBody:
                                    $ref: '#/components/schemas/Request'
                                signatureImg:
                                    type: string
                                    format: base64
                                minorSignatureImg:
                                    type: string
                                    format: base64
                                delegForm:
                                    type: string
                                delegatePhotoId:
                                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    consentPdf:
                                        type: string
                                    requestId:
                                        type: string
                "400":
                    description: Bad Request
                "401":
                    description: Unauthorized
                "404":
                    description: Not Found
                "500":
                    description: Internal Server Error
            security:
                -   jwtBearer: []
    /v1/requests/incomplete:
        post:
            summary: Create an Incomplete Request
            description: Create a new incomplete request to save request data.
            operationId: post-v1-requests-incomplete
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/IncompleteRequest'
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/IncompleteRequestInitResponse'
                "400":
                    description: Bad Request
                "500":
                    description: Internal Server Error
    /v1/requests/incomplete/{incompleteRequestId}:
        get:
            summary: Get Metadata of the Incomplete Request
            description: Get metadata of a specific incomplete request.
            operationId: get-v1-requests-incomplete-incompleteRequestId
            parameters:
                -   name: incompleteRequestId
                    in: path
                    required: true
                    style: simple
                    explode: false
                    schema:
                        type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/IncompleteRequestMetadata'
                "404":
                    description: Not Found
                "500":
                    description: Internal Server Error
        put:
            summary: Update an Incomplete Request
            description: Update an incomplete request with updated request data
            operationId: patch-v1-requests-incomplete-incompleteRequestId
            parameters:
                -   name: Authorization
                    in: header
                    description: Auth token returned from the incomplete/:id/verify endpoint
                    style: simple
                    explode: false
                    schema:
                        type: string
                -   name: incompleteRequestId
                    in: path
                    required: true
                    style: simple
                    explode: false
                    schema:
                        type: string
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/IncompleteRequest'
            responses:
                "200":
                    description: OK
                "400":
                    description: Bad Request
                "404":
                    description: Not Found
                "500":
                    description: Internal Server Error
    /v1/requests/incomplete/{incompleteRequestId}/email:
        get:
            summary: Get Incomplete Request Data for an Email
            description: Get and format Incomplete Request data for usage in an email template.
            operationId: get-v1-requests-incomplete-incompleteRequestId-email
            parameters:
                -   name: Authorization
                    in: header
                    description: JWT Token added to the Email Queue message
                    style: simple
                    explode: false
                    schema:
                        type: string
                -   name: incompleteRequestId
                    in: path
                    required: true
                    style: simple
                    explode: false
                    schema:
                        type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    status:
                                        type: string
                                    data:
                                        type: object
                                        properties:
                                            incomplete_request_id:
                                                type: string
                                            clinic_name:
                                                type: string
                                required:
                                    - status
                "404":
                    description: Not Found
                "500":
                    description: Internal Server Error
    /v1/requests/incomplete/{incompleteRequestId}/email/status:
        post:
            summary: Callback to notify of a successful/failed email send
            description: Callback endpoint to notify Core API of whether an email send was successful or not.
            operationId: post-v1-requests-incomplete-incompleteRequestId-email-status
            parameters:
                -   name: Authorization
                    in: header
                    style: simple
                    explode: false
                    schema:
                        type: string
                -   name: incompleteRequestId
                    in: path
                    required: true
                    style: simple
                    explode: false
                    schema:
                        type: string
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/EmailStatusCallback'
            responses:
                "200":
                    description: OK
                "400":
                    description: Bad Request
                "401":
                    description: Unauthorized
                "500":
                    description: Internal Server Error
    /v1/requests/incomplete/{incompleteRequestId}/verify:
        post:
            summary: Authenticate Access to Incomplete Request
            description: Authenticate a patient's access to incomplete request data by verifying their input DOB against the saved request data.
            operationId: post-v1-requests-incomplete-incompleteRequestId-verify
            parameters:
                -   name: incompleteRequestId
                    in: path
                    required: true
                    style: simple
                    explode: false
                    schema:
                        type: string
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/IncompleteRequestVerify'
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/IncompleteRequestResponse'
                "400":
                    description: Bad Request
                "404":
                    description: Not Found
                "500":
                    description: Internal Server Error
    /v1/requests/uph:
        post:
            summary: Create a new UPH request
            description: |-
                Initiate a new UPH request.
                Returns PDF form including signature.
            operationId: post-requests-uph
            requestBody:
                content:
                    multipart/form-data:
                        schema:
                            $ref: '#/components/schemas/NewUPHRequest'
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                description: Signed request form
                                type: object
                                properties:
                                    requestId:
                                        type: string
                                    consentPdf:
                                        type: string
                                        format: binary
                                    registerPin:
                                        type: string
                                        deprecated: true
                "400":
                    description: Bad Request
                "402":
                    description: Payment Required
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/PaymentError'
                "500":
                    description: Internal Server Error
    /v1/requests/verification/config:
        get:
            summary: Get all request verification configurations
            description: Get all request verification configurations
            operationId: get-v1-requests-verification-configuration
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: array
                                items:
                                    $ref: '#/components/schemas/VerificationConfig'
                "500":
                    description: Internal Server Error
    /v1/shares:
        get:
            summary: Get shares for a user
            description: As a logged in patient, get a list of all shares made.
            operationId: get-shares
            parameters:
                -   name: limit
                    in: query
                    description: For pagination
                    style: form
                    explode: true
                    schema:
                        type: integer
                -   name: offset
                    in: query
                    description: For pagination
                    style: form
                    explode: true
                    schema:
                        type: integer
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Share'
                "401":
                    description: Unauthorized
                "403":
                    description: Forbidden
                "500":
                    description: Internal Server Error
            security:
                -   jwtBearer: []
        post:
            summary: Create a new share
            description: Create a new share for a logged in user
            operationId: post-shares
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/Share'
            responses:
                "200":
                    description: OK
                "400":
                    description: Bad Request
                "401":
                    description: Unauthorized
                "403":
                    description: Forbidden
                "500":
                    description: Internal Server Error
            security:
                -   jwtBearer: []
    /v1/shares/{shareId}:
        get:
            summary: Get share files
            description: |-
                3 options:
                - Set Accept header to application/zip to download zip file.
                - Set Accept header to application/octet-stream to download iso file.
                - Set Accept header to application/json to get json body response (ie, eUnity token, exam list)
            operationId: get-shares-byId
            parameters:
                -   name: eids
                    in: query
                    description: comma separated list of exam ids to download. ignored for json option.
                    style: form
                    explode: true
                    schema:
                        type: string
                -   name: shareId
                    in: path
                    required: true
                    style: simple
                    explode: false
                    schema:
                        type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Share'
                        application/zip:
                            schema:
                                type: string
                                format: binary
                "400":
                    description: Bad Request
                "401":
                    description: Unauthorized
                "403":
                    description: Forbidden
                "404":
                    description: Not Found
                "500":
                    description: Internal Server Error
            security:
                -   jwtBearer: []
    /v1/shares/{shareId}/dltoken:
        post:
            summary: POST share download auth
            description: Get a limited token that authorizes the bearer to download a share. Endpoint must be authenticated.
            operationId: post-v1-shares-shareId-dltoken
            parameters:
                -   name: shareId
                    in: path
                    required: true
                    style: simple
                    explode: false
                    schema:
                        type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/DLToken'
                "401":
                    description: Unauthorized
                "404":
                    description: Not Found
                "500":
                    description: Internal Server Error
            security:
                -   jwtBearer: []
    /v1/shares/{shareId}/extend:
        put:
            summary: Extend the expiry date
            description: Extend the expiry date for the share by the given share id
            operationId: put-share-extend-byshareId
            parameters:
                -   name: shareId
                    in: path
                    required: true
                    style: simple
                    explode: false
                    schema:
                        type: string
            responses:
                "200":
                    description: OK
                "401":
                    description: Unauthorized
                "500":
                    description: Internal Server Error
            security:
                -   jwtBearer: []
    /v1/shares/{shareId}/healthrecords:
        get:
            summary: Get shares healthrecord files as a single zip
            description: Retrieve a zipped folder (organized by record) of all the records for given FHIR ID's.
            operationId: get-shares-healthrecords-byId
            parameters:
                -   name: hrids
                    in: query
                    description: the FHIR ID's to get data for in a comma separated list
                    style: form
                    explode: true
                    schema:
                        type: string
                -   name: shareId
                    in: path
                    required: true
                    style: simple
                    explode: false
                    schema:
                        type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/zip:
                            schema:
                                type: string
                                format: binary
                "400":
                    description: Bad Request
                "401":
                    description: Unauthorized
                "500":
                    description: Internal Server Error
            security:
                -   jwtBearer: []
    /v1/shares/{shareId}/reshare:
        post:
            summary: Reshare
            description: Regenerate the print page, refax, or re-email an existing share (performed by logged in patient)
            operationId: post-reshare
            parameters:
                -   name: shareId
                    in: path
                    required: true
                    style: simple
                    explode: false
                    schema:
                        type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/pdf:
                            schema:
                                description: response will only be populated for print shares
                                type: string
                                format: binary
                "401":
                    description: Unauthorized
                "403":
                    description: Forbidden
                "404":
                    description: Not Found
                "500":
                    description: Internal Server Error
            security:
                -   jwtBearer: []
    /v1/shares/{shareId}/revoke:
        put:
            summary: Revoke share permissions
            description: Revoke share permissions (performed by a logged in patient).
            operationId: put-revoke-share
            parameters:
                -   name: shareId
                    in: path
                    required: true
                    style: simple
                    explode: false
                    schema:
                        type: string
            responses:
                "200":
                    description: OK
                "401":
                    description: Unauthorized
                "403":
                    description: Forbidden
                "404":
                    description: Not Found
                "500":
                    description: Internal Server Error
            security:
                -   jwtBearer: []
    /v1/shares/validate:
        post:
            summary: Validate share viewer
            description: 'Validate a share viewer - For email viewers: with shareID and pin; For print viewers: with viewcode and DOB; For extended print viewers, with lastName and token in Authorization header. 200 response returns a token.'
            operationId: post-shares-validate
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/ShareCredentials'
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                description: |
                                    token
                                type: object
                                properties:
                                    token:
                                        type: string
                                    requiresNameAuth:
                                        type: boolean
                "400":
                    description: Bad Request
                "500":
                    description: Internal Server Error
    /v1/short-urls:
        post:
            summary: Generate a slug for the provided URL
            description: Generate a slug for the provided URL
            operationId: post-short-urls
            requestBody:
                content:
                    application/json:
                        schema:
                            type: object
                            properties:
                                url:
                                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ShortUrl'
                "400":
                    description: Bad Request
                "401":
                    description: Unauthorized
                "422":
                    description: Unprocessable Entity
                "500":
                    description: Internal Server Error
            security:
                -   phSignature: []
    /v1/short-urls/{slug}:
        get:
            summary: Get the original URL for the provided slug
            description: Get the original URL for the provided slug
            operationId: get-short-urls
            parameters:
                -   name: slug
                    in: path
                    required: true
                    style: simple
                    explode: false
                    schema:
                        type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ShortUrl'
                "400":
                    description: Bad Request
                "404":
                    description: Not Found
                "500":
                    description: Internal Server Error
    /v1/transactions/providers:
        get:
            summary: Get payment providers
            description: Get a list of available providers for a country
            operationId: get-v1-expertreview-paymentproviders
            parameters:
                -   name: country
                    in: query
                    description: Available providers in the country
                    style: form
                    explode: true
                    schema:
                        type: string
                        pattern: CA|US
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: array
                                items:
                                    type: string
                                minItems: 1
                                uniqueItems: true
                "401":
                    description: Unauthorized
                "500":
                    description: Internal Server Error
    /v1/transfers/{transferId}:
        get:
            summary: Get transfer info
            description: Get the details for the transfer - whether it requires purchasing & the fee details. Requires authentication via the transfers challenge
            operationId: get-transfers-transferId
            parameters:
                -   name: transferId
                    in: path
                    required: true
                    style: simple
                    explode: false
                    schema:
                        type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Transfer'
                "401":
                    description: Unauthorized
                "404":
                    description: Not Found
                "500":
                    description: Internal Server Error
            deprecated: true
            security:
                -   jwtBearer: []
        delete:
            summary: Remove a transfer
            description: |-
                Removes a transfer and refunds the patient, if they've paid. This is ONLY possible for self-uploaded transfers DURING the self uploading process (it's a fallback in case there are errors in the multi-step process that require earlier steps to be rolled back.)
                Requires the uploadSessionID header.
            operationId: delete-transfers-transferId
            parameters:
                -   name: uploadSessionId
                    in: header
                    style: simple
                    explode: false
                    schema:
                        type: string
                -   name: transferId
                    in: path
                    required: true
                    style: simple
                    explode: false
                    schema:
                        type: string
            responses:
                "200":
                    description: OK
                "401":
                    description: Unauthorized
                "404":
                    description: Not Found
                "500":
                    description: Internal Server Error
            deprecated: true
            security:
                -   jwtBearer: []
    /v1/transfers/{transferId}/challenge:
        get:
            summary: Get transfer challenge question
            description: Get the challenge question for a transfer.
            operationId: get-transfers-transferId-challenge
            parameters:
                -   name: transferId
                    in: path
                    required: true
                    style: simple
                    explode: false
                    schema:
                        type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ChallengeQuestion'
                "404":
                    description: Not Found
                "500":
                    description: Internal Server Error
            deprecated: true
        post:
            summary: Answer Transfer Challenge
            description: |-
                User challenge answer to verify their identity for a new exam that's available.
                Returns a token that can be used to a) register a new user (for out of portal challenge) or b) purchase the records (in portal challenge)
            operationId: post-transfers-transferId-challenge
            parameters:
                -   name: transferId
                    in: path
                    required: true
                    style: simple
                    explode: false
                    schema:
                        type: string
            requestBody:
                description: Challenge answer
                content:
                    application/json:
                        schema:
                            description: challenge question answer
                            type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ChallengeToken'
                "400":
                    description: Bad Request
                "401":
                    description: Unauthorized
                "403":
                    description: Forbidden
                "404":
                    description: Not Found
                "500":
                    description: Internal Server Error
            deprecated: true
    /v1/transfers/{transferId}/purchase:
        post:
            summary: Pay for transfer
            description: |-
                Pay for self uploaded exam or enrolled transfer (in portal purchase).
                Two types of authorization:
                - [DEPRECATED] for self uploaded exams, expects normal user authentication with a valid uploadSessionId header (retrieved from POST /transfers)
                - otherwise, a transfer token received from doing a successful transfer challenge (GET/POST /trabsfers/{id}/challenge)
                - paymentProvider name in PurchaseToken is required to know actual transaction took place and not just transfer activation
            operationId: post-transfer-payment
            parameters:
                -   name: transferId
                    in: path
                    required: true
                    style: simple
                    explode: false
                    schema:
                        type: string
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/PaymentToken'
            responses:
                "200":
                    description: OK
                "400":
                    description: Bad Request
                "401":
                    description: Unauthorized
                "402":
                    description: Payment Required
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/PaymentError'
                "403":
                    description: Forbidden
                "404":
                    description: Not Found
                "500":
                    description: Internal Server Error
            deprecated: true
            security:
                -   jwtBearer: []
    /v1/transfers/{transferId}/reactivate:
        post:
            summary: Reactivate the expired Transfer
            operationId: post-transfers-transferId-reactivate
            parameters:
                -   name: transferId
                    in: path
                    required: true
                    style: simple
                    explode: false
                    schema:
                        type: string
            responses:
                "200":
                    description: OK
                "500":
                    description: Internal Server Error
            deprecated: true
    /v1/upload-request:
        get:
            description: Get details about the upload request (limited to information relevant to the uploader). ID is retrieved from the JWT.
            operationId: get-upload-request-v1
            responses:
                "200":
                    description: Successfully got the upload request.
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/GetUploadRequestResponse'
                "401":
                    description: Invalid Authentication.
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ErrorResponse'
                "404":
                    description: Upload Request not found.
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ErrorResponse'
        post:
            description: Create an unsolicited upload request and create a session JWT for this request.
            operationId: create-upload-request-v1
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/CreateUploadRequestBody'
                required: true
            responses:
                "200":
                    description: Successfully created a new upload request.
                    content:
                        application/json:
                            schema:
                                type: object
                "400":
                    description: Bad Request.
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ErrorResponse'
        patch:
            description: Update an upload request's contact details and/or provider destination id.
            operationId: update-upload-request-v1
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/UpdateUploadRequestBody'
                required: true
            responses:
                "200":
                    description: Successfully updated the Upload Request.
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/GetUploadRequestResponse'
                "400":
                    description: Invalid request body.
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ErrorResponse'
                "401":
                    description: Invalid Authentication.
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ErrorResponse'
                "403":
                    description: Destination set by provider and cannot be changed.
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ErrorResponse'
                "404":
                    description: Upload Request not found.
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ErrorResponse'
                "409":
                    description: The Upload Request is not in a state where it can be changed.
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ErrorResponse'
    /v1/upload-request/authenticate:
        post:
            description: Authenticate a security code and birth date for a solicited upload request and create a session JWT for this request.
            operationId: authenticate-upload-request-v1
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/AuthenticateUploadRequestBody'
                required: true
            responses:
                "200":
                    description: Successfully authenticated upload request.
                    content:
                        application/json:
                            schema:
                                type: object
                "404":
                    description: No valid request found.
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ErrorResponse'
    /v1/upload-request/decline:
        post:
            description: Decline a solicited upload request with a reason.
            operationId: decline-upload-request-v1
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/DeclineUploadRequestBody'
                required: true
            responses:
                "200":
                    description: Successfully declined the upload request.
                    content:
                        application/json:
                            schema:
                                type: object
                "404":
                    description: Upload Request not found.
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ErrorResponse'
                "409":
                    description: The Upload Request is not in a state where it can be declined.
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ErrorResponse'
    /v1/upload-request/instances:
        post:
            description: Uploads an instance for this upload request.
            operationId: upload-request-upload-instance-v1
            requestBody:
                content:
                    multipart/form-data:
                        schema:
                            type: object
                            properties:
                                file:
                                    type: string
                                    format: binary
                            required:
                                - file
                required: true
            responses:
                "200":
                    description: Successfully uploaded the instance.
                    content:
                        application/json:
                            schema:
                                type: object
                "400":
                    description: Bad Request.
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ErrorResponse'
                "401":
                    description: Invalid Authentication.
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ErrorResponse'
                "404":
                    description: Upload Request not found.
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ErrorResponse'
                "409":
                    description: The upload request is not in a state where instances can be uploaded.
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ErrorResponse'
    /v1/upload-request/providers/{subdomain}:
        get:
            description: Get record retrieval metadata for providers with record retrieval configured.
            operationId: get-provider-details-v1
            parameters:
                -   name: subdomain
                    in: path
                    description: The subdomain associated with the provider.
                    required: true
                    style: simple
                    explode: false
                    schema:
                        type: string
            responses:
                "200":
                    description: Successfully got the list of studies for this upload request.
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/GetProviderDetailsResponse'
                "404":
                    description: The provider was not found or does not have record retrieval enabled.
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ErrorResponse'
    /v1/upload-request/studies:
        get:
            description: Get a list of studies associated with this upload request.
            operationId: list-upload-request-studies-v1
            responses:
                "200":
                    description: Successfully got the list of studies for this upload request.
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/UploadRequestStudiesResponse'
                "401":
                    description: Invalid Authentication.
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ErrorResponse'
                "404":
                    description: Upload Request not found.
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ErrorResponse'
    /v1/upload-request/studies/{id}:
        get:
            description: Get a study by id from this upload request.
            operationId: get-upload-request-study-by-id-v1
            parameters:
                -   name: id
                    in: path
                    description: The id of the study to get from this upload request.
                    required: true
                    style: simple
                    explode: false
                    schema:
                        type: string
            responses:
                "200":
                    description: Successfully got the study from its id.
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/UploadRequestStudyResponse'
                "401":
                    description: Invalid Authentication.
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ErrorResponse'
                "404":
                    description: Upload Request or Study not found.
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ErrorResponse'
        delete:
            description: Delete a study from an upload request.
            operationId: delete-upload-request-study-v1
            parameters:
                -   name: id
                    in: path
                    description: The id of the study to delete from this upload request.
                    required: true
                    style: simple
                    explode: false
                    schema:
                        type: string
            responses:
                "204":
                    description: Successfully deleted the study from the upload request.
                "400":
                    description: The study is not associated with this upload request.
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ErrorResponse'
                "401":
                    description: Invalid Authentication.
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ErrorResponse'
                "404":
                    description: Upload Request or Study not found.
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ErrorResponse'
                "409":
                    description: The Upload Request is not in a state where study notes can be changed.
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ErrorResponse'
        patch:
            description: Update study details in an upload request.
            operationId: update-upload-request-study-v1
            parameters:
                -   name: id
                    in: path
                    description: The id of the study to update within this upload request.
                    required: true
                    style: simple
                    explode: false
                    schema:
                        type: string
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/SubmitStudyNotesBody'
                required: true
            responses:
                "200":
                    description: Successfully updated the study.
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/UploadRequestStudyResponse'
                "401":
                    description: Invalid Authentication.
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ErrorResponse'
                "404":
                    description: Upload Request or Study not found.
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ErrorResponse'
                "409":
                    description: The Upload Request is not in a state where studies can be changed.
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ErrorResponse'
    /v1/upload-request/submit:
        post:
            description: Submit the upload request to the provider.
            operationId: submit-upload-request-v1
            responses:
                "200":
                    description: Successfully submitted the upload request.
                    content:
                        application/json:
                            schema:
                                type: object
                "401":
                    description: Invalid Authentication.
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ErrorResponse'
                "404":
                    description: Upload Request not found.
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ErrorResponse'
                "409":
                    description: The upload request is not in a state where it can be submitted.
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ErrorResponse'
    /v1/users:
        post:
            summary: Create a user
            description: Create a new user
            operationId: post-users-userId
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/RegisterData'
            responses:
                "200":
                    description: OK
                "400":
                    description: Bad Request
                "401":
                    description: Unauthorized
                "500":
                    description: Internal Server Error
    /v1/users/consents:
        get:
            summary: Get consent pdf(s)
            description: |-
                Retrieve the consent pdf associated with a records request, transfer, or provider.
                Use only one of the query parameters to retrieve a consent PDF, or use none, and retrieve all a user's consent forms.
            operationId: get-user-request-consent
            parameters:
                -   name: requestID
                    in: query
                    style: form
                    explode: true
                    schema:
                        type: string
                -   name: providerID
                    in: query
                    style: form
                    explode: true
                    schema:
                        type: integer
                -   name: transferID
                    in: query
                    style: form
                    explode: true
                    schema:
                        type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/pdf:
                            schema:
                                type: string
                                format: binary
                "401":
                    description: Unauthorized
                "403":
                    description: Forbidden
                "404":
                    description: Not Found
                "500":
                    description: Internal Server Error
            security:
                -   jwtBearer: []
    /v1/users/exams:
        get:
            summary: Get exams
            description: |-
                Get a list of all uploaded exams for the current user.
                Use query parameter to filter by activated or locked exams. Default is activated.
            operationId: get-user-exams
            parameters:
                -   name: activated
                    in: query
                    description: filter exams by whether or not they are activated
                    style: form
                    explode: true
                    schema:
                        type: boolean
                -   name: include_reports
                    in: query
                    description: Include reports in response
                    style: form
                    explode: true
                    schema:
                        type: boolean
                -   name: uid
                    in: query
                    description: Filter by UID
                    style: form
                    explode: false
                    schema:
                        type: string
                -   name: account_id
                    in: query
                    description: Filter by account ID
                    style: form
                    explode: true
                    schema:
                        type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: array
                                items:
                                    $ref: '#/components/schemas/Exam'
                "401":
                    description: Unauthorized
                "403":
                    description: Forbidden
                "404":
                    description: Not Found
                "500":
                    description: Internal Server Error
            security:
                -   jwtBearer: []
                -   phSignature: []
    /v1/users/exams/{examId}:
        get:
            summary: Get exam
            description: Get exam by ID, including image and report ID lists
            operationId: get-user-exam-byId
            parameters:
                -   name: examId
                    in: path
                    required: true
                    style: simple
                    explode: false
                    schema:
                        type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Exam'
                "401":
                    description: Unauthorized
                "403":
                    description: Forbidden
                "404":
                    description: Not Found
                "500":
                    description: Internal Server Error
            security:
                -   jwtBearer: []
    /v1/users/exams/{examId}/organviz:
        get:
            summary: Get organ visualizations for exam
            description: Get organ visualizations for all reports in an exam
            operationId: get-organ-visualization-by-exam-id
            parameters:
                -   name: examId
                    in: path
                    required: true
                    style: simple
                    explode: false
                    schema:
                        type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    organs:
                                        type: array
                                        items:
                                            $ref: '#/components/schemas/OrganVisualization'
                "401":
                    description: Unauthorized
                "404":
                    description: Not Found
                "500":
                    description: Internal Server Error
            security:
                -   jwtBearer: []
    /v1/users/exams/{examuuid}/thumbnail:
        get:
            summary: Get exam thumbnail
            description: For a logged in user with a specific exam uuid or an authenticated share viewer, returns a PNG thumbnail of the first file for said exam
            parameters:
                -   name: examuuid
                    in: path
                    required: true
                    style: simple
                    explode: false
                    schema:
                        type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/png:
                            schema:
                                type: string
                                format: binary
                "401":
                    description: Unauthorized
                "404":
                    description: Not Found
            security:
                -   jwtBearer: []
    /v1/users/exams/eligible-insights:
        get:
            summary: Get exam insight eligibility statuses for patient
            description: Get an object describing all the eligible exams insights for the given account, or patient.
            parameters:
                -   name: patient_id
                    in: query
                    style: form
                    explode: true
                    schema:
                        type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: array
                                items:
                                    $ref: '#/components/schemas/ExamInsightEligibilityResponse'
                "400":
                    description: Bad Request
                "401":
                    description: Unauthorized
                "404":
                    description: Not Found
    /v1/users/exams/lookup:
        get:
            summary: Lookup an exam by SSO token and accession
            description: 'Royal portal integration: Lookup an exam via sso token and accession, returns transferId if exam is found, otherwise 404.'
            operationId: get-users-exams-lookup
            parameters:
                -   name: token
                    in: query
                    description: sso token
                    style: form
                    explode: true
                    schema:
                        type: string
                -   name: accession
                    in: query
                    description: accession number
                    style: form
                    explode: true
                    schema:
                        type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                description: transferId
                                type: string
                "400":
                    description: Bad Request
                "401":
                    description: Unauthorized
                "404":
                    description: Not Found
                "500":
                    description: Internal Server Error
            deprecated: true
            security:
                -   jwtBearer: []
    /v1/users/exams/size:
        get:
            summary: Get all exam files' size
            description: Get the total size of all images and reports in a users account
            operationId: get-users-exams-size
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                description: size of all images/reports
                                type: number
                "401":
                    description: Unauthorized
                "500":
                    description: Internal Server Error
            security:
                -   jwtBearer: []
    /v1/users/login/sso:
        post:
            summary: Login via SSO
            description: Login with an SSO token
            operationId: post-users-login-sso
            requestBody:
                content:
                    application/json:
                        schema:
                            description: sso token
                            type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    token:
                                        type: string
                                    uuid:
                                        type: string
                                    userExists:
                                        type: boolean
                "400":
                    description: Bad Request
                "401":
                    description: Unauthorized
                "403":
                    description: Forbidden
                "500":
                    description: Internal Server Error
    /v1/users/logout:
        delete:
            summary: Logout
            description: Log out the current user
            operationId: delete-users-logout
            responses:
                "200":
                    description: OK
                "500":
                    description: Internal Server Error
            security:
                -   jwtBearer: []
    /v1/users/notifications:
        get:
            summary: Get user notifications
            description: Get a list of all user unread notifications.
            operationId: get-user-notifications
            parameters:
                -   name: include_read
                    in: query
                    style: form
                    explode: true
                    schema:
                        type: boolean
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Notifications'
                "401":
                    description: Unauthorized
                "403":
                    description: Forbidden
                "404":
                    description: Not Found
                "500":
                    description: Internal Server Error
        post:
            summary: Create a new user notification
            operationId: post-user-notification
            parameters:
                -   name: include_read
                    in: query
                    style: form
                    explode: true
                    schema:
                        type: boolean
            responses:
                "200":
                    description: OK
                "201":
                    description: Created
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Notification'
                "400":
                    description: Bad Request
                "401":
                    description: Unauthorized
                "500":
                    description: Internal Server Error
    /v1/users/notifications/{notificationId}:
        put:
            summary: Mark notification read
            description: Mark notification read with the given ID
            operationId: put-notification-read-byId
            parameters:
                -   name: notificationId
                    in: path
                    required: true
                    style: simple
                    explode: false
                    schema:
                        type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/NotificationRead'
                "401":
                    description: Unauthorized
                "403":
                    description: Forbidden
                "404":
                    description: Not Found
                "500":
                    description: Internal Server Error
        delete:
            summary: Delete the current notification
            parameters:
                -   name: notificationId
                    in: path
                    required: true
                    style: simple
                    explode: false
                    schema:
                        type: string
            responses:
                "200":
                    description: OK
                "401":
                    description: Unauthorized
                "404":
                    description: Not Found
                "500":
                    description: Internal Server Error
    /v1/users/referral:
        post:
            summary: Refer a friend
            description: Refer a friend to join PocketHealth
            operationId: post-users-referral
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/Referral'
            responses:
                "200":
                    description: OK
                "500":
                    description: Internal Server Error
            security:
                -   jwtBearer: []
    /v1/users/reportinsights:
        get:
            summary: Get available/generated report insight in account, or patient
            description: Get an object describing all the available/generated report insights for the given account, or patient.
            parameters:
                -   name: patient_id
                    in: query
                    style: form
                    explode: true
                    schema:
                        type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/InsightsResponse'
                "401":
                    description: Unauthorized
                "404":
                    description: Not Found
    /v1/users/rollout:
        get:
            summary: Get users ep rollout
            description: |-
                get launch flag value probabilistically for rollout flag (intended to be used by
                front-end to decide to call RR or coreapi (then forward to acctsvc).
            operationId: get-users-rollout-flag
            parameters:
                -   name: flag
                    in: query
                    description: flag of rollout, either 'resetpw' or 'login'
                    required: true
                    style: form
                    explode: true
                    schema:
                        type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: boolean
                "500":
                    description: Internal Server Error
    /v1/users/settings:
        get:
            summary: Get user settings
            description: Get user settings
            operationId: get-users-settings
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    reportNotification:
                                        type: boolean
                                    showOnboarding:
                                        type: boolean
                                    onboardingTasks:
                                        $ref: '#/components/schemas/OnboardingTaskCompletion'
                                    language:
                                        type: string
                "401":
                    description: Unauthorized
                "500":
                    description: Internal Server Error
            security:
                -   jwtBearer: []
        put:
            summary: Update user settings
            description: update users report-notification setting, onboarding experience task completion, or language setting
            operationId: update-user-settings
            requestBody:
                content:
                    application/json:
                        schema:
                            type: object
                            properties:
                                reportNotification:
                                    type: boolean
                                task:
                                    $ref: '#/components/schemas/OnboardingTask'
                                language:
                                    type: string
            responses:
                "200":
                    description: OK
                "401":
                    description: Unauthorized
                "500":
                    description: Internal Server Error
            security:
                -   jwtBearer: []
    /v1/users/subscription/toggleautorenew:
        put:
            summary: Toggle auto-renew
            description: Toggle auto-renew on a subscription.
            operationId: put-users-subscription-toggleautorenew
            responses:
                "200":
                    description: OK
                "401":
                    description: Unauthorized
                "500":
                    description: Internal Server Error
            security:
                -   jwtBearer: []
    /v2/features/{id}/authorize:
        get:
            summary: Authorize a Feature
            description: One query param must be provided. Auth token gets validated if `user_id` query param provided.
            operationId: get-v1-features-id-authorize
            parameters:
                -   name: user_id
                    in: query
                    style: form
                    explode: true
                    schema:
                        type: number
                -   name: org_id
                    in: query
                    style: form
                    explode: true
                    schema:
                        type: number
                -   name: id
                    in: path
                    required: true
                    style: simple
                    explode: false
                    schema:
                        type: integer
            responses:
                "200":
                    description: OK
    /v2/healthrecords/{patientId}:
        get:
            summary: Get Health Records
            description: 'Get health records metadata for a signle patient '
            operationId: get-v2-patientId
            parameters:
                -   name: covidVaccine
                    in: query
                    description: If set to true, an email is sent to the user prompting them to refer others to upload their vaccine record.
                    style: form
                    explode: true
                    schema:
                        type: boolean
                -   name: patientId
                    in: path
                    required: true
                    style: simple
                    explode: false
                    schema:
                        type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    id:
                                        type: string
                                    lastModified:
                                        type: string
                                    acctId:
                                        type: string
                                    phPatientId:
                                        type: string
                                    record:
                                        $ref: '#/components/schemas/Record'
                "404":
                    description: Not Found
                "500":
                    description: Internal Server Error
        post:
            summary: post health records
            description: Upload health records for a single patient
            operationId: post-v2-healthrecords-patientId
            parameters:
                -   name: patientId
                    in: path
                    required: true
                    style: simple
                    explode: false
                    schema:
                        type: string
            requestBody:
                description: |-
                    The request body should have

                    1. The records field
                    2. For each record, a file whose name matches a filename in records.
                content:
                    multipart/form-data:
                        schema:
                            type: object
                            properties:
                                records:
                                    type: array
                                    items: {}
                                file:
                                    type: string
            responses:
                "200":
                    description: OK
                "400":
                    description: Bad Request
                "401":
                    description: Unauthorized
                "500":
                    description: Internal Server Error
    /v2/healthrecords/{patientId}/integrations/mychart:
        post:
            summary: post new records integration with mychart
            description: Connect an account to MyChart for the patient identified by patientId
            operationId: post-v2-healthrecords-mychart-patientId
            parameters:
                -   name: patientId
                    in: path
                    required: true
                    style: simple
                    explode: false
                    schema:
                        type: string
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/MyChartIntegrationRequest'
            responses:
                "200":
                    description: OK
                "400":
                    description: Bad Request
                "401":
                    description: Unauthorized
                "500":
                    description: Internal Server Error
    /v2/healthrecords/{patientId}/questionnaire/gail:
        post:
            summary: post health records questionnaire response
            description: Get Gail model results for patient
            operationId: post-v2-healthrecords-patientId-gail
            parameters:
                -   name: patientId
                    in: path
                    required: true
                    style: simple
                    explode: false
                    schema:
                        type: string
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/GailQuestionnaireResponse'
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/GailRiskScore'
                "400":
                    description: Bad Request
                "401":
                    description: Unauthorized
                "422":
                    description: Unprocessable Entity
                "500":
                    description: Internal Server Error
    /v2/healthrecords/{patientId}/record/type/{recordType}:
        get:
            summary: get records belonging to a patient of a specific fhir type
            description: Return the health records of a specific fhir resource type, belonging to a patient
            operationId: get-v2-healthrecords-patientId-records-recordType
            parameters:
                -   name: patientId
                    in: path
                    required: true
                    style: simple
                    explode: false
                    schema:
                        type: string
                -   name: recordType
                    in: path
                    required: true
                    style: simple
                    explode: false
                    schema:
                        type: string
                -   name: limit
                    in: query
                    description: max amount of results to return
                    style: form
                    explode: true
                    schema:
                        type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: array
                                items:
                                    $ref: '#/components/schemas/Record'
                "401":
                    description: Unauthorized
    /v2/healthrecords/{patientId}/records/{recordId}:
        get:
            summary: Get a zip of all files for a health record
            description: For a logged in user with a specific record ID or an authenticated share viewer, returns all files as 1 zip for said record
            operationId: get-v2-healthrecords-patientId-records-recordId
            parameters:
                -   name: patientId
                    in: path
                    required: true
                    style: simple
                    explode: false
                    schema:
                        type: string
                -   name: recordId
                    in: path
                    required: true
                    style: simple
                    explode: false
                    schema:
                        type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/zip:
                            schema:
                                type: string
                                format: binary
                "401":
                    description: Unauthorized
        put:
            summary: update an existing health record
            description: For a logged in user with a specific record ID, update said record
            operationId: put-v2-healthrecords-patientId-records-recordId
            parameters:
                -   name: patientId
                    in: path
                    required: true
                    style: simple
                    explode: false
                    schema:
                        type: string
                -   name: recordId
                    in: path
                    required: true
                    style: simple
                    explode: false
                    schema:
                        type: string
            requestBody:
                content:
                    multipart/form-data:
                        schema:
                            type: object
                            properties:
                                records:
                                    $ref: '#/components/schemas/Record'
                                file:
                                    type: string
            responses:
                "200":
                    description: OK
                "400":
                    description: Bad Request
                "401":
                    description: Unauthorized
                "500":
                    description: Internal Server Error
        delete:
            summary: delete health record
            operationId: delete-v2-healthrecords-patientId-records-recordId
            parameters:
                -   name: patientId
                    in: path
                    required: true
                    style: simple
                    explode: false
                    schema:
                        type: string
                -   name: recordId
                    in: path
                    required: true
                    style: simple
                    explode: false
                    schema:
                        type: string
            responses:
                "200":
                    description: OK
                "400":
                    description: Bad Request
                "401":
                    description: Unauthorized
                "404":
                    description: Not Found
                "500":
                    description: Internal Server Error
    /v2/healthrecords/{patientId}/records/{recordId}/thumbnail:
        get:
            summary: get thumbnail of the first attachment of a health record
            description: For a logged in user with a specific record ID or an authenticated share viewer, returns a PNG thumbnail of the first file for said record
            operationId: get-v2-healthrecords-patientId-records-recordId-thumbnail
            parameters:
                -   name: patientId
                    in: path
                    required: true
                    style: simple
                    explode: false
                    schema:
                        type: string
                -   name: recordId
                    in: path
                    required: true
                    style: simple
                    explode: false
                    schema:
                        type: string
            responses:
                "200":
                    description: OK
                "401":
                    description: Unauthorized
    /v2/healthrecords/{patientId}/upload:
        get:
            summary: Get upload session token
            description: For a logged in user eligible for health records feature, returns a health records upload session token for a specific profile
            operationId: get-v2-healthrecords-patientId-upload
            parameters:
                -   name: patientId
                    in: path
                    description: PH Patient ID of health record being uploaded
                    required: true
                    style: simple
                    explode: false
                    schema:
                        type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    uploadSessionToken:
                                        type: string
                                    regionID:
                                        type: number
                "401":
                    description: Unauthorized
    /v2/healthrecords/mychart/search:
        get:
            summary: search mychart org name
            description: search for a mychart org based on its name
            operationId: get-v2-healthrecords-mychart-search
            parameters:
                -   name: query
                    in: query
                    required: true
                    style: form
                    explode: true
                    schema:
                        type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: array
                                items:
                                    $ref: '#/components/schemas/OrgInfo'
                "400":
                    description: Bad Request
                "401":
                    description: Unauthorized
                "403":
                    description: Forbidden
                "500":
                    description: Internal Server Error
    /v2/healthrecords/verify:
        post:
            description: verify that an upload token is valid
            operationId: post-v2-healthrecords-verify
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    valid:
                                        type: boolean
                "401":
                    description: Unauthorized
    /v2/orders:
        get:
            summary: Get account orders
            description: |-
                Get active recurring order for an account, if any. Use account from JWT. Returns empty list if no orders.
                TODO: when needed, can open up this EP to allow filtering on the active/recurring parameters of orders. That's why return body is an array.
            operationId: get-v2-orders
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: array
                                items:
                                    $ref: '#/components/schemas/Order'
                "401":
                    description: Unauthorized
                "500":
                    description: Internal Server Error
            security:
                -   jwtBearer: []
        post:
            summary: Create order
            description: Create an order where the customer is charged
            operationId: post-v2-orders
            parameters:
                -   name: reason
                    in: query
                    description: A reason the new subscription is being created; may be useful for analytics
                    style: form
                    explode: true
                    schema:
                        type: string
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/OrderRequest'
                    application/xml:
                        schema:
                            type: object
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/CreateOrderResponse'
                "400":
                    description: Bad Request
                "401":
                    description: Unauthorized
                "404":
                    description: Not Found
                "500":
                    description: Internal Server Error
            security:
                -   jwtBearer: []
    /v2/orders/paymentDetails:
        put:
            summary: Update payment details
            description: Update payment details of auto renewal for current session order
            operationId: put-v2-orders-paymentDetails
            requestBody:
                description: Stripe token
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/PaymentToken'
            responses:
                "200":
                    description: OK
                "400":
                    description: Bad Request
                "401":
                    description: Unauthorized
                "500":
                    description: Internal Server Error
            security:
                -   jwtBearer: []
    /v2/patients:
        get:
            summary: Get patient profiles
            description: Get list of patient profiles for an account
            operationId: get-patient-profiles
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: array
                                items:
                                    $ref: '#/components/schemas/Patient'
                "400":
                    description: Bad Request
                "401":
                    description: Unauthorized
                "404":
                    description: Not Found
                "500":
                    description: Internal Server Error
            security:
                -   jwtBearer: []
        post:
            summary: Add a patient
            description: Add a patient to an account
            operationId: post-patient-profiles
            requestBody:
                content:
                    application/x-www-form-urlencoded:
                        schema:
                            $ref: '#/components/schemas/Patient'
            responses:
                "200":
                    description: OK
                "400":
                    description: Bad Request
                "401":
                    description: Unauthorized
                "403":
                    description: Forbidden
                "404":
                    description: Not Found
                "500":
                    description: Internal Server Error
            security:
                -   jwtBearer: []
    /v2/patients/{patient_id}/valid:
        get:
            summary: Check if a patient has a valid profile
            parameters:
                -   name: patient_id
                    in: path
                    required: true
                    style: simple
                    explode: false
                    schema:
                        type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/PatientProfileValidation'
                "400":
                    description: Bad Request
                "401":
                    description: Unauthorized
                "404":
                    description: Not Found
                "500":
                    description: Internal Server Error
    /v2/patients/{patientId}:
        get:
            summary: Get patient profile by id
            description: Get list of patient profiles for account
            operationId: get-patient-profiles-id
            parameters:
                -   name: patientId
                    in: path
                    required: true
                    style: simple
                    explode: false
                    schema:
                        type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Patient'
                "400":
                    description: Bad Request
                "401":
                    description: Unauthorized
                "404":
                    description: Not Found
                "500":
                    description: Internal Server Error
            security:
                -   jwtBearer: []
        delete:
            summary: Delete patient profile by id
            description: Delete a patient profile by id
            operationId: delete-patient-profile
            parameters:
                -   name: patientId
                    in: path
                    required: true
                    style: simple
                    explode: false
                    schema:
                        type: string
            responses:
                "200":
                    description: OK
                "400":
                    description: Bad Request
                "401":
                    description: Unauthorized
                "404":
                    description: Not Found
                "500":
                    description: Internal Server Error
            security:
                -   jwtBearer: []
        patch:
            summary: Update patient profile by id
            description: Update a patient profile by id
            operationId: patch-patient-profile
            parameters:
                -   name: patientId
                    in: path
                    required: true
                    style: simple
                    explode: false
                    schema:
                        type: string
            requestBody:
                content:
                    application/x-www-form-urlencoded:
                        schema:
                            $ref: '#/components/schemas/Patient'
            responses:
                "200":
                    description: OK
                "400":
                    description: Bad Request
                "401":
                    description: Unauthorized
                "404":
                    description: Not Found
                "500":
                    description: Internal Server Error
            security:
                -   jwtBearer: []
    /v2/patients/incomplete/notification:
        post:
            summary: Create notifications for an account that has incomplete patients
            responses:
                "200":
                    description: OK
                "201":
                    description: Created
                "401":
                    description: Unauthorized
                "500":
                    description: Internal Server Error
    /v2/patients/valid:
        get:
            summary: Check if all patients in an account have a valid profile
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: object
                                additionalProperties:
                                    $ref: '#/components/schemas/PatientProfileValidation'
                "400":
                    description: Bad Request
                "401":
                    description: Unauthorized
                "404":
                    description: Not Found
                "500":
                    description: Internal Server Error
    /v2/plans:
        get:
            summary: Get a List of Available Plans
            description: Get a list of available plans filtered by `recurring`, which defaults to true.
            operationId: get-v1-plans
            parameters:
                -   name: recurring
                    in: query
                    style: form
                    explode: true
                    schema:
                        type: boolean
            responses:
                "200":
                    description: OK
            security:
                -   jwtBearer: []
    /v2/providers/{id}/plans:
        get:
            summary: Get provider plans
            description: Get list of plan objs supported by a provider
            operationId: get-v2-providers
            parameters:
                -   name: id
                    in: path
                    required: true
                    style: simple
                    explode: false
                    schema:
                        type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: array
                                items:
                                    $ref: '#/components/schemas/Plan'
                "500":
                    description: Internal Server Error
    /v2/requests/{rid}/status/history:
        post:
            summary: Get all request status history records
            description: |-
                Create a new request.
                Returns PDF form including signature.
            parameters:
                -   name: rid
                    in: path
                    required: true
                    style: simple
                    explode: false
                    schema:
                        type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: array
                                items:
                                    $ref: '#/components/schemas/RequestStatusHistory'
                "400":
                    description: Bad Request
                "401":
                    description: Unauthorized
    /v2/requests/create:
        post:
            summary: Create a new request
            description: |-
                Create a new request.
                Returns PDF form including signature.
            requestBody:
                content:
                    multipart/form-data:
                        schema:
                            $ref: '#/components/schemas/CreateRequest'
            responses:
                "201":
                    description: created
                    content:
                        application/json:
                            schema:
                                description: New request identifier and consent file
                                type: object
                                properties:
                                    requestId:
                                        type: string
                                    consentPdf:
                                        type: string
                                        format: binary
                "400":
                    description: Bad Request
                "401":
                    description: Unauthorized
                "402":
                    description: Payment Required
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/PaymentError'
                "500":
                    description: Internal Server Error
    /v2/rho/{examId}:
        post:
            summary: Initiate Rho Request
            description: Submit new rho request for an exam
            operationId: post-v2-rho-request
            parameters:
                -   name: examId
                    in: path
                    required: true
                    style: simple
                    explode: false
                    schema:
                        type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: string
                "401":
                    description: Unauthorized
                "500":
                    description: Internal Server Error
    /v2/rho/eligible/{examId}:
        get:
            summary: Rho Exam Eligibility
            description: Get the eligibility of an exam for rho from examinsights
            operationId: get-v2-rho-eligible-examId
            parameters:
                -   name: examId
                    in: path
                    required: true
                    style: simple
                    explode: false
                    schema:
                        type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/AnalysisEligibility'
                "400":
                    description: Bad Request
                "401":
                    description: Unauthorized
                "404":
                    description: Not Found
                "422":
                    description: Unprocessable Entity
                "500":
                    description: Internal Server Error
    /v2/secondopinion/doctors/search:
        get:
            summary: Search for CPSO doctors from second opinion svc
            operationId: get-v2-secondopiniont-doctors-search
            parameters:
                -   name: query
                    in: query
                    description: search term
                    style: form
                    explode: true
                    schema:
                        type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: array
                                items:
                                    $ref: '#/components/schemas/SOCPSODoctor'
                "401":
                    description: Unauthorized
                "500":
                    description: Internal Server Error
    /v2/secondopinion/eligible/{examId}:
        get:
            summary: Second Opinion Exam Eligibility
            description: Get the eligibility of an exam for second opinion from examinsights
            operationId: get-v1-secondopinion-eligible-examId
            parameters:
                -   name: examId
                    in: path
                    required: true
                    style: simple
                    explode: false
                    schema:
                        type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/SOEligibility'
    /v2/secondopinion/eligible/priors:
        post:
            summary: Check if list of priors are eligible
            description: Check if list of prior exams are eligible for second opinion base on ruleset we have in backend.
            operationId: post-v2-secondopinion-eligible-priors
            requestBody:
                content:
                    application/json:
                        schema:
                            type: object
                            properties:
                                examUuids:
                                    type: array
                                    items:
                                        type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: array
                                items:
                                    type: string
                "401":
                    description: Unauthorized
                "500":
                    description: Internal Server Error
    /v2/secondopinion/patient_eligibility:
        post:
            summary: Create a complete / incomplete PEP for a specific patient
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/CreatePatientEligibilityProgramRequest'
            responses:
                "201":
                    description: Created
                "400":
                    description: Bad Request
                "401":
                    description: Not Authorized
                "404":
                    description: Not Found
                "500":
                    description: Internal Server Error
    /v2/secondopinion/patient_eligibility/{patientId}/{programName}:
        put:
            summary: Update uncompleted Patient Eligibility Programs for patient
            parameters:
                -   name: patientId
                    in: path
                    required: true
                    style: simple
                    explode: false
                    schema:
                        type: string
                -   name: programName
                    in: path
                    required: true
                    style: simple
                    explode: false
                    schema:
                        type: string
            requestBody:
                content:
                    application/json:
                        schema:
                            type: object
                            properties:
                                is_completed:
                                    type: boolean
            responses:
                "200":
                    description: OK
                "400":
                    description: Bad Request
                "401":
                    description: Unauthorized
                "404":
                    description: Not found
                "500":
                    description: Internal Server Error
    /v2/secondopinion/patient_eligibility/{programName}:
        post:
            summary: Create new patient eligibility programs for an account
            parameters:
                -   name: programName
                    in: path
                    description: program name of the patient eligibility to be created
                    required: true
                    style: simple
                    explode: false
                    schema:
                        type: string
            responses:
                "200":
                    description: OK
                "201":
                    description: Created
                "400":
                    description: Bad Request
                "500":
                    description: Internal Server Error
    /v2/secondopinion/review/{examid}:
        post:
            summary: Initiate new SO review request
            description: Initiate a new Second Opinion review request. Returns back the review id created.
            operationId: post-v1-secondopinion-reviews
            parameters:
                -   name: examid
                    in: path
                    required: true
                    style: simple
                    explode: false
                    schema:
                        type: string
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/SOReviewRequest'
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: string
                "400":
                    description: Bad Request
                    content:
                        application/json:
                            schema:
                                type: object
                "401":
                    description: Unauthorized
                    content:
                        application/json:
                            schema:
                                type: object
                "500":
                    description: Internal Server Error
            security:
                -   jwtBearer: []
    /v2/shares/{shareId}/exams:
        get:
            summary: Get exams and health records within a share
            description: Get a list of exams and health records associated with a share for a validated share viewer
            operationId: get-share-exams-byshareId-v2
            parameters:
                -   name: shareId
                    in: path
                    required: true
                    style: simple
                    explode: false
                    schema:
                        type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    exams:
                                        type: array
                                        items:
                                            $ref: '#/components/schemas/Exam'
                                    hrs:
                                        type: array
                                        items:
                                            $ref: '#/components/schemas/Record'
                                    hrPatient:
                                        $ref: '#/components/schemas/HRPatient'
                "401":
                    description: Unauthorized
                "500":
                    description: Internal Server Error
    /v2/transfers:
        post:
            summary: Initialize a V2 upload
            description: Initialize a V2 upload with the list of images to upload. Returns the transfer and upload session IDs.
            operationId: post-v2-transfers
            requestBody:
                content:
                    application/json:
                        schema:
                            type: array
                            items:
                                $ref: '#/components/schemas/UploadImage'
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/UploadInitResponse'
                "400":
                    description: Bad Request
                "401":
                    description: Unauthorized
                "500":
                    description: Internal Server Error
            security:
                -   jwtBearer: []
    /v2/transfers/{transferId}:
        delete:
            summary: V2 upload rollback
            description: Refunds payment if made and calls providers service to roll back the upload. Ends upload session.
            operationId: delete-v2-transfers-transferId
            parameters:
                -   name: upload-session-id
                    in: header
                    style: simple
                    explode: false
                    schema:
                        type: string
                -   name: transferId
                    in: path
                    required: true
                    style: simple
                    explode: false
                    schema:
                        type: string
            responses:
                "200":
                    description: OK
                "401":
                    description: Unauthorized
                "404":
                    description: Not Found
                "500":
                    description: Internal Server Error
            security:
                -   jwtBearer: []
    /v2/transfers/{transferId}/finalize:
        post:
            summary: Finalize upload
            description: Finalize upload - polls provider service until complete/timeout, then finalizes transfer. Ends upload session.
            operationId: post-v2-transfers-transferId-finalize
            parameters:
                -   name: upload-session-id
                    in: header
                    style: simple
                    explode: false
                    schema:
                        type: string
                -   name: transferId
                    in: path
                    required: true
                    style: simple
                    explode: false
                    schema:
                        type: string
            responses:
                "200":
                    description: OK
                "401":
                    description: Unauthorized
                "404":
                    description: Not Found
                "500":
                    description: Internal Server Error
            security:
                -   jwtBearer: []
    /v2/transfers/{transferId}/images:
        post:
            summary: Upload images to transfer
            description: Upload images to transfer.
            operationId: post-v2-transfers-transferId-file
            parameters:
                -   name: upload-session-id
                    in: header
                    style: simple
                    explode: false
                    schema:
                        type: string
                -   name: transferId
                    in: path
                    required: true
                    style: simple
                    explode: false
                    schema:
                        type: string
            requestBody:
                description: Attached files
                content:
                    multipart/form-data:
                        schema:
                            type: object
                            properties:
                                file:
                                    type: array
                                    items:
                                        type: string
                                        format: binary
            responses:
                "200":
                    description: OK - Files accepted for processing.
                "400":
                    description: Bad Request
                "401":
                    description: Unauthorized
                "404":
                    description: Not Found
                "500":
                    description: Internal Server Error
            security:
                -   jwtBearer: []
    /v2/transfers/{transferId}/reportdcm:
        post:
            summary: Upload dcm report to transfer
            description: Upload a single dcm report to a transfer
            operationId: post-v2-transfers-transferId-reportdcm
            parameters:
                -   name: upload-session-id
                    in: header
                    style: simple
                    explode: false
                    schema:
                        type: string
                -   name: transferId
                    in: path
                    required: true
                    style: simple
                    explode: false
                    schema:
                        type: string
            requestBody:
                content:
                    multipart/form-data:
                        schema:
                            type: object
                            properties:
                                file:
                                    type: string
                                    format: binary
                                metadata:
                                    $ref: '#/components/schemas/UploadFileMetadata'
            responses:
                "200":
                    description: OK
                "400":
                    description: Bad Request
                "401":
                    description: Unauthorized
                "404":
                    description: Not Found
                "500":
                    description: Internal Server Error
            security:
                -   jwtBearer: []
    /v2/users/deactivate:
        patch:
            summary: Deactivate account
            description: Put account in an inaccessible frozen state to be picked up for deletion later
            operationId: patch-users-deactivate-account
            responses:
                "200":
                    description: OK
                "400":
                    description: Bad request ( token absent in body )
                "401":
                    description: Unauthorized
                "404":
                    description: Not Found
                "500":
                    description: Internal Server Error
            security:
                -   jwtBearer: []
    /v2/users/email-verification:
        post:
            summary: Post email verification
            description: Check if email is verified using the given token
            operationId: post-email-verification
            requestBody:
                content:
                    application/json:
                        schema:
                            description: token
                            type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/EmailVerification'
                "400":
                    description: Bad Request
                "500":
                    description: Internal Server Error
    /v2/users/email/update:
        put:
            summary: Update user's email
            description: Take the given token and call PATCH /v1/accounts in acctsvc to update the email
            operationId: put-users-update-email
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/Verify'
            responses:
                "200":
                    description: OK
                "400":
                    description: Bad request ( token absent in body )
                "500":
                    description: Internal Server Error
    /v2/users/email/update/init:
        post:
            description: Update user account email. Backend will enforce token age, so should get new authentication token just prior to making this request.
            operationId: post-v2users-updateEmail
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/V2EmailUpdate'
            responses:
                "200":
                    description: OK
            security:
                -   jwtBearer: []
    /v2/users/exams/lookup:
        get:
            summary: Lookup an exam by SSO token and accession
            description: 'Royal portal integration: Lookup an exam via sso token and accession, returns uuid if exam is found, otherwise 404.'
            operationId: get-v2-users-exams-lookup
            parameters:
                -   name: token
                    in: query
                    description: sso token
                    style: form
                    explode: true
                    schema:
                        type: string
                -   name: accession
                    in: query
                    description: accession number
                    style: form
                    explode: true
                    schema:
                        type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                description: uuid
                                type: string
                "400":
                    description: Bad Request
                "401":
                    description: Unauthorized
                "404":
                    description: Not Found
                "500":
                    description: Internal Server Error
            security:
                -   jwtBearer: []
    /v2/users/lockAccount:
        post:
            summary: Lock User account upon email update
            description: Take the lock token and call /v1/accounts/lock in acctsvc to lock the account after account email update
            operationId: post-users-lock-account
            requestBody:
                content:
                    application/json:
                        schema:
                            description: lock token
                            type: string
            responses:
                "200":
                    description: OK
                "400":
                    description: Bad request ( token absent in body )
                "401":
                    description: Unauthorized
                "404":
                    description: Token not found
                "500":
                    description: Internal Server Error
    /v2/users/owner:
        patch:
            summary: Set Account Owner
            description: Set Account Owner to Patient Id specified in request body
            operationId: patch-users-set-account-owner
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/SetAccountOwnerRequest'
            responses:
                "200":
                    description: OK
                "400":
                    description: Bad request ( token absent in body )
                "401":
                    description: Unauthorized
                "404":
                    description: Not Found
                "500":
                    description: Internal Server Error
            security:
                -   jwtBearer: []
    /v2/users/resetpassword:
        post:
            summary: Reset password
            description: Reset the password using a token and security code
            operationId: post-v2-users-resetpassword
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/PasswordResetInfo'
            responses:
                "200":
                    description: OK
                "400":
                    description: Bad Request
    /v2/users/resetpassword/init:
        post:
            summary: Init reset password
            description: Initiate the reset password process to send an email to the user with a link to change their password.
            operationId: post-v2-users-resetpassword-init
            requestBody:
                content:
                    application/json:
                        schema:
                            description: email to send reset instructions to
                            type: string
            responses:
                "200":
                    description: OK
    /v2/users/setup:
        post:
            summary: Set user password and verify email
            description: Take the given verification token and password and call /v1/accounts/verify in acctsvc to set the password and verify the email
            operationId: post-users-setup
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/Setup'
            responses:
                "200":
                    description: OK
                "400":
                    description: Bad Password or invalid request body
    /v2/users/state:
        get:
            summary: get account info and state
            description: get account state
            operationId: get-account-state
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/AccountState'
                "401":
                    description: Unauthorized
                "500":
                    description: Internal Server Error
            security:
                -   jwtBearer: []
    /v2/users/verify:
        post:
            summary: Verify user email
            description: Take the given verification token and call /v1/accounts/verify in acctsvc to verify the email
            operationId: post-users-verify
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/Verify'
            responses:
                "200":
                    description: OK
                "400":
                    description: Bad request ( token absent in body )
    /v2/users/verify/dob:
        post:
            summary: Post verify DOB
            description: Verify DOB answered is correct
            operationId: post-verify-dob
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/DOBVerification'
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                description: Whether DOB is correct
                                type: boolean
                "400":
                    description: Bad Request
                "500":
                    description: Internal Server Error
components:
    schemas:
        AccountState:
            type: object
            properties:
                created_at:
                    type: string
                language:
                    type: string
        AnalysisEligibility:
            type: object
            properties:
                eligible:
                    description: exam eligible to rho
                    type: boolean
                status:
                    type: string
                    enum:
                        - "not started"
                        - "submitted"
                        - "in progress"
                        - "rejected"
                        - "completed"
                        - "blocked"
                        - "error"
                        - "unknown status"
                request_id:
                    description: rho request id if exam has existing rho request
                    type: string
        Appointment:
            description: An appointment with metadata
            type: object
            properties:
                id:
                    type: integer
                description:
                    type: string
                time:
                    type: string
                prep_instruction_text:
                    type: string
                prep_instruction_urls:
                    type: array
                    items:
                        type: string
                modality:
                    type: string
                procedure:
                    type: string
            required:
                - description
                - time
                - modality
                - procedure
        AppointmentDetails:
            description: A set of appointments for a patient. They should all be in the same clinic on the same day
            type: object
            properties:
                reminder_id:
                    type: string
                patient_name:
                    type: string
                appointment_date:
                    type: string
                    format: date
                patient_status:
                    $ref: '#/components/schemas/PatientStatus'
                clinic:
                    $ref: '#/components/schemas/ClinicInfo'
                appointments:
                    type: array
                    items:
                        $ref: '#/components/schemas/Appointment'
            required:
                - reminder_id
                - patient_name
                - appointment_date
                - patient_status
                - clinic
                - appointments
        AppointmentReminderPatientStatusUpdate:
            description: Object that contains the id of an appointment reminder and the patient status it should be updated to
            type: object
            properties:
                reminder_id:
                    type: string
                patient_status:
                    $ref: '#/components/schemas/PatientStatus'
            required:
                - reminder_id
                - patient_status
        AuthenticateUploadRequestBody:
            description: The fields required to identify and authenticate a solicited upload request
            type: object
            properties:
                security_code:
                    type: string
                    pattern: ^[a-zA-Z0-9]{9}$
                birth_date:
                    type: string
                    format: date
            required:
                - security_code
                - birth_date
        BreastBiopsyFactor:
            type: object
            properties:
                exists:
                    $ref: '#/components/schemas/YesNoUnknown'
                benign_biopsies:
                    $ref: '#/components/schemas/OneOrMore'
                atypical_hyperplasia:
                    $ref: '#/components/schemas/OneOrMore'
        ChallengeQuestion:
            type: object
            properties:
                question:
                    type: string
                userExists:
                    type: boolean
                activated:
                    type: boolean
                examId:
                    type: string
                questionType:
                    type: string
        ChallengeToken:
            type: object
            properties:
                token:
                    type: string
                userExists:
                    type: boolean
                userEmail:
                    type: string
                examId:
                    type: string
        ClinicInfo:
            description: Clinic information for appointments
            type: object
            properties:
                name:
                    type: string
                clinic_name_short:
                    type: string
                address:
                    type: string
                address_details:
                    type: string
                address_short:
                    type: string
                phone:
                    type: string
                email:
                    type: string
                url:
                    type: string
        Consent:
            type: object
            properties:
                opt:
                    type: string
                fullName:
                    type: string
                signatureImg:
                    description: base64 encoded signature image
                    type: string
                    format: base64
        ConsentEmailVerification:
            type: object
            properties:
                token:
                    type: string
                email:
                    type: string
        ConsentFormData:
            type: object
            properties:
                providerName:
                    type: string
                new:
                    type: boolean
                opt:
                    type: string
                consentText:
                    type: string
                understandItems:
                    type: array
                    items:
                        type: string
                appointmentReminderId:
                    type: string
                consentSource:
                    type: string
        ConsentType:
            description: The consent form type (verified or unverified)
            type: object
            properties:
                verified:
                    type: boolean
        CreateOrderResponse:
            type: object
            properties:
                order_id:
                    type: string
                provider_sub_id:
                    type: string
                provider_cust_id:
                    type: string
                provider_txn_id:
                    type: string
        CreatePatientEligibilityProgramRequest:
            type: object
            properties:
                patient_id:
                    type: string
                program_name:
                    type: string
                is_completed:
                    description: The newly created PEP should be marked as completed
                    type: boolean
                with_notification:
                    description: If an uncompleted PEP then create a notification for it.
                    type: boolean
        CreateRequest:
            type: object
            properties:
                requestDetails:
                    $ref: '#/components/schemas/Request'
                paymentToken:
                    $ref: '#/components/schemas/PaymentToken'
                signatureImg:
                    description: signature image base64
                    type: string
                    format: base64
                minorSignatureImg:
                    description: delegate patient signature image base64
                    type: string
                    format: base64
                delegateForm:
                    description: delegate supporting evidence file
                    type: string
                delegatePhotoId:
                    description: delegate supporting photo id ( only 1 file allowed to be uploaded)
                    type: string
                subscribeToConnect:
                    description: Whether to use the payment details to subscribe the user to Connect
                    type: boolean
                    deprecated: true
        CreateUploadRequestBody:
            description: The fields required to create an unsolicited upload request
            type: object
            properties:
                provider_id:
                    type: integer
                    format: int64
                provider_destination_id:
                    type: integer
                    format: int64
                patient_name:
                    type: string
                patient_birth_date:
                    type: string
                    format: date
                uploader_type:
                    $ref: '#/components/schemas/UploadRequestUploaderType'
                contact_name:
                    type: string
                contact_method:
                    $ref: '#/components/schemas/UploadRequestUploaderContactMethod'
                contact_information:
                    type: string
            required:
                - provider_id
                - provider_destination_id
                - patient_name
                - patient_birth_date
                - uploader_type
                - contact_name
                - contact_method
                - contact_information
        CurrentCondition:
            type: string
            enum:
                - "DCIS"
                - "LCIS"
                - "0"
        DLToken:
            type: object
            properties:
                token:
                    type: string
        DOBVerification:
            type: object
            properties:
                accountId:
                    type: string
                dateOfBirth:
                    type: string
        DeclineUploadRequestBody:
            description: The fields for declining an upload request
            type: object
            properties:
                security_code:
                    type: string
                    pattern: ^[a-zA-Z0-9]{9}$
                birth_date:
                    type: string
                    format: date
                decline_reason:
                    type: string
                    minLength: 1
            required:
                - security_code
                - birth_date
                - decline_reason
        DicomUID:
            description: A DICOM standard UID format string.
            type: string
            maxLength: 64
            minLength: 1
            pattern: ^[0-9.]*$
        EmailStatusCallback:
            type: object
            properties:
                is_success:
                    type: boolean
                error:
                    description: error message for failed email
                    type: string
            required:
                - is_success
        EmailVerification:
            type: object
            properties:
                accountId:
                    type: string
                isVerified:
                    type: boolean
        Error:
            type: object
            properties:
                code:
                    description: |-
                        Machine-readable, service-specific error code (enum)
                        A service-specific string constant (enum) representing the type of error that has been encountered. Generally automatically interpreted by the caller (as opposed to displayed to the user). The convention used for these enum values is typically an all-caps, snake-cased string. See the provided examples.
                        Be careful when adding new codes so as to not leak information that attackers could use to compromise our services.
                    type: string
                message:
                    description: |-
                        Human-readable error message
                        A human-readable error message explaining the error code.
                        When crafting human-readable error messages, please stick to the following guidelines: https://www.notion.so/pockethealth/User-facing-error-message-style-guide-1e00d4c6794380af96a4d99f2270d49e?pvs=4
                        Be careful when providing detail in these error messages so as to not leak information that attackers could use to compromise our services.
                    type: string
                validation_errors:
                    type: array
                    items:
                        $ref: '#/components/schemas/ValidationError'
                additional_details:
                    summary: Arbitrary error-related details whose schema depends on the specific error code
                    nullable: true
                context:
                    $ref: '#/components/schemas/ErrorContext'
            required:
                - code
        ErrorContext:
            description: Context relating to a request/response to facilitate tracing/debugging
            type: object
            properties:
                trace_id:
                    description: |-
                        Trace/correlation ID for use in tracing/debugging
                        A trace/correlation ID used in tracing the causes of specific errors.
                        If a trace/correlation ID is supplied as part of the initial request, most PocketHealth services should use that same correlation ID throughout the lifecycle of handling that request - including returning the same correlation ID. If one was not supplied, services' middleware should automatically generate one.
                        See https://opentelemetry.io/docs/concepts/signals/traces/
                    type: string
                span_id:
                    description: |-
                        Span ID for use in tracing/debugging
                        A span ID used in tracing the causes of specific errors.
                        A span usually relates to a specific unit of work. Spans are composed together to form traces.
                        See https://opentelemetry.io/docs/concepts/signals/traces/#spans
                    type: string
            required:
                - trace_id
        ErrorResponse:
            type: object
            properties:
                message:
                    type: string
                code:
                    type: string
            required:
                - message
                - code
        Exam:
            type: object
            properties:
                series:
                    type: array
                    items:
                        $ref: '#/components/schemas/Series'
                reports:
                    type: array
                    items:
                        $ref: '#/components/schemas/Report'
                examId:
                    description: Not populated on POST
                    type: string
                patientName:
                    $ref: '#/components/schemas/PatientName'
                examType:
                    type: string
                provider:
                    type: string
                examDate:
                    type: string
                activated:
                    description: Not populated on POST
                    type: boolean
                description:
                    type: string
                dob:
                    type: string
                sex:
                    type: string
                transferId:
                    type: string
                transferStatus:
                    type: string
                orgId:
                    type: integer
                bodyPart:
                    type: string
                reportDelay:
                    description: When reportDelay is -1, the organization does not send reports.
                    type: integer
                uuid:
                    type: string
                referringPhysician:
                    type: string
                size:
                    type: integer
                modality:
                    description: unparsed, abbreviated exam modality
                    type: string
        ExamInsightEligibility:
            type: object
            properties:
                rho:
                    type: boolean
                second_opinion:
                    type: boolean
                report:
                    type: boolean
                followups:
                    type: array
                    items:
                        type: string
        ExamInsightEligibilityResponse:
            type: object
            additionalProperties:
                $ref: '#/components/schemas/ExamInsightEligibility'
        FaxStatus:
            description: Fax Status enum
            type: string
            enum:
                - "delivered"
                - "failed"
                - "no-answer"
                - "busy"
                - "canceled"
        FeeInfo:
            type: object
            properties:
                feeAmount:
                    type: number
                    deprecated: true
                taxName:
                    type: string
                    deprecated: true
                taxPercent:
                    type: number
                    format: float
                    deprecated: true
                region:
                    type: string
        GailQuestionnaireResponse:
            type: object
            properties:
                eligibility_check:
                    $ref: '#/components/schemas/RiskEligibility'
                ethnicity:
                    description: |
                        Ethnicity:
                         * `Wh` - White
                         * `AA` - African American
                         * `H` - Hispanic
                         * `NA` - Native American or Unknown
                         * `A` - Asian
                    type: string
                    enum:
                        - "Wh"
                        - "AA"
                        - "H"
                        - "NA"
                        - "A"
                sub_ethnicity:
                    description: |
                        Ethnicity:
                         * `HU` - Hispanic American
                         * `HF` - hispanic american foreign born
                         * `Ch` - chinese american
                         * `Ja` - japanese american
                         * `Fi` - filipino american
                         * `Hw` - hawaiian american
                         * `oP` - other pacific islander
                         * `oA` - other asian
                    type: string
                    enum:
                        - "HU"
                        - "HF"
                        - "Ch"
                        - "Ja"
                        - "Fi"
                        - "Hw"
                        - "oP"
                        - "oA"
                age_men:
                    description: age of first menstruation
                    type: number
                age_birth:
                    description: current age
                    type: number
                first_degree_relatives:
                    $ref: '#/components/schemas/OneOrMore'
                breast_biopsy:
                    $ref: '#/components/schemas/BreastBiopsyFactor'
            required:
                - eligibility_check
                - ethnicity
                - sub_ethnicity
                - age_men
                - age_birth
                - first_degree_relatives
                - breast_biopsy
        GailRiskScore:
            type: object
            properties:
                risk_five_years:
                    type: number
                risk_lifetime:
                    type: number
        GetProviderDetailsResponse:
            description: Response object containing provider details
            type: object
            properties:
                id:
                    type: integer
                    format: int64
                name:
                    type: string
                logo:
                    $ref: '#/components/schemas/ProviderLogo'
                destinations:
                    type: array
                    items:
                        $ref: '#/components/schemas/ProviderDestination'
            required:
                - id
                - name
                - logo
                - destinations
        GetUploadRequestResponse:
            description: The Upload Request details that an uploader can retrieve.
            type: object
            properties:
                provider_destination_id:
                    type: integer
                    format: int64
                provider_destination_set_by:
                    $ref: '#/components/schemas/UploadRequestDestinationSetBy'
                request_type:
                    $ref: '#/components/schemas/UploadRequestType'
                state:
                    $ref: '#/components/schemas/UploadRequestState'
                reference_code:
                    type: string
                patient_name:
                    type: string
                patient_birth_date:
                    type: string
                    format: date
                uploader_type:
                    $ref: '#/components/schemas/UploadRequestUploaderType'
                uploader_contact_name:
                    type: string
                uploader_contact_method:
                    $ref: '#/components/schemas/UploadRequestUploaderContactMethod'
                uploader_contact_information:
                    type: string
            required:
                - request_type
                - state
                - reference_code
                - patient_name
                - patient_birth_date
        HRPatient:
            description: Patient for HRs
            type: object
            properties:
                phUserId:
                    type: integer
                phProfileId:
                    type: integer
                firstAndMiddle:
                    type: string
                lastName:
                    type: string
        Image:
            type: object
            properties:
                ImageId:
                    type: string
                Token:
                    type: string
        ImageMetadata:
            type: object
            properties:
                referringPhysician:
                    type: string
                type:
                    type: string
                bodyPart:
                    type: string
                description:
                    type: string
                clinicName:
                    type: string
                date:
                    type: string
                clinicId:
                    type: integer
                activated:
                    type: boolean
                examId:
                    type: string
        IncompleteRequest:
            type: object
            properties:
                requestData:
                    $ref: '#/components/schemas/Request'
                lastCompletedStep:
                    $ref: '#/components/schemas/RequestFormStep'
            required:
                - requestData
                - lastCompletedStep
        IncompleteRequestInitResponse:
            type: object
            properties:
                incompleteRequestId:
                    type: string
                authToken:
                    type: string
            required:
                - incompleteRequestId
                - authToken
        IncompleteRequestMetadata:
            type: object
            properties:
                clinicName:
                    type: string
                status:
                    type: string
                    enum:
                        - "INCOMPLETE"
                        - "COMPLETE"
                        - "EXPIRED"
                emailStep:
                    type: string
                clinicId:
                    type: number
            required:
                - clinicName
                - status
                - emailStep
                - clinicId
        IncompleteRequestResponse:
            type: object
            properties:
                requestData:
                    $ref: '#/components/schemas/Request'
                lastCompletedStep:
                    $ref: '#/components/schemas/RequestFormStep'
                authToken:
                    type: string
            required:
                - requestData
                - lastCompletedStep
                - authToken
        IncompleteRequestVerify:
            type: object
            properties:
                dob:
                    description: Date of birth of patient associated with request.
                    type: string
                data:
                    description: JWE Token with request data
                    type: string
            required:
                - dob
                - data
        InsightsResponse:
            type: object
            properties:
                reports:
                    type: object
                    additionalProperties:
                        $ref: '#/components/schemas/ReportInsight'
                organviz_count:
                    $ref: '#/components/schemas/TotalOrganViz'
        MyChartIntegrationRequest:
            description: MyChart Health Records integration request
            type: object
            properties:
                code:
                    description: access code from mychart oauth flow
                    type: string
            required:
                - code
        NewRequest:
            type: object
            properties:
                requestBody:
                    $ref: '#/components/schemas/Request'
                paymentToken:
                    $ref: '#/components/schemas/PaymentToken'
                signatureImg:
                    description: signature image base64
                    type: string
                    format: base64
                delegForm:
                    description: delegate supporting evidence file
                    type: string
                delegatePhotoId:
                    description: delegate supporting photo id ( only 1 file allowed to be uploaded)
                    type: string
                subscribeToConnect:
                    description: Whether to use the payment details to subscribe the user to Connect
                    type: boolean
        NewUPHRequest:
            type: object
            properties:
                requestBody:
                    $ref: '#/components/schemas/Request'
                paymentToken:
                    $ref: '#/components/schemas/PaymentToken'
                signatureImg:
                    description: signature image base64
                    type: string
                    format: base64
                subscribeToConnect:
                    description: Whether to use the payment details to subscribe the user to Connect
                    type: boolean
        Notification:
            type: object
            properties:
                id:
                    type: string
                type:
                    type: string
                read:
                    type: boolean
                patient_id:
                    type: string
                created_date:
                    type: string
                    format: date-time
        NotificationMethod:
            description: Notification method for physician accounts
            type: string
            enum:
                - "Fax"
                - "Email"
        NotificationRead:
            type: object
            properties:
                read:
                    type: boolean
        Notifications:
            type: object
            properties:
                notifications:
                    type: array
                    items:
                        $ref: '#/components/schemas/Notification'
        OnboardingTask:
            description: TaskVideo=1, TaskRequest=2, TaskUpload=4, TaskConnect=8
            type: integer
            format: uint
            enum:
                - 1
                - 2
                - 4
                - 8
        OnboardingTaskCompletion:
            type: object
            properties:
                completeVideo:
                    type: boolean
                completeRequest:
                    type: boolean
                completeUpload:
                    type: boolean
                completeConnect:
                    type: boolean
        OneOrMore:
            description: |
                OneOrMore:
                  * `0` - none
                  * `1` - one
                  * `2` - more than one
                  * `99` - unknown
            type: number
            enum:
                - 0
                - 1
                - 2
                - 99
        Order:
            type: object
            properties:
                order_id:
                    type: string
                plan_id:
                    type: integer
                is_auto_renewing:
                    type: boolean
                created_at:
                    type: string
                expires_at:
                    type: string
                payment_method:
                    $ref: '#/components/schemas/PaymentCard'
        OrderRequest:
            type: object
            properties:
                plan_id:
                    type: integer
                token:
                    type: string
        OrgInfo:
            type: object
            properties:
                id:
                    description: Id of the Org
                    type: string
                display_name:
                    description: name of the Org
                    type: string
                api_url:
                    description: url required to make a mychart connection with the org
                    type: string
        OrganVisualization:
            type: object
            properties:
                body_part:
                    description: body_part of the segmentation
                    type: string
                segmentation:
                    description: segmentation image, as base64 string
                    type: string
                status:
                    description: status of the segmentation inference, IN_PROGRESS, COMPLETED, ERROR
                    type: string
        OrganVisualizationRequest:
            type: object
            properties:
                body_parts:
                    description: list of body parts that we want to get segmentations on
                    type: array
                    items:
                        type: string
                series_uid:
                    description: series_uid that we want to pick the image slice from
                    type: string
                object_id:
                    description: object_id that we want to use as for the segmentation
                    type: string
            required:
                - body_parts
        OrganViz:
            type: object
            properties:
                num_masks_generated:
                    type: integer
                model:
                    type: string
        PasswordResetInfo:
            type: object
            properties:
                token:
                    type: string
                securityCode:
                    type: string
                newPassword:
                    type: string
        Patient:
            type: object
            properties:
                patientId:
                    type: string
                firstName:
                    type: string
                lastName:
                    type: string
                altLastName:
                    type: string
                DOB:
                    type: string
                ohip:
                    type: string
                ohipvc:
                    type: string
                bcphn:
                    type: string
                altId:
                    type: string
                phone:
                    type: string
                email:
                    type: string
                ipn:
                    type: string
                ssn:
                    type: string
                sex:
                    type: string
                country:
                    type: string
                subdivision:
                    description: essentially state/province
                    type: string
                postalCode:
                    type: string
                isAccountOwner:
                    type: boolean
        PatientName:
            type: object
            properties:
                dicomName:
                    type: string
                firstAndMiddleName:
                    type: string
                lastName:
                    type: string
        PatientProfileValidation:
            type: object
            properties:
                valid:
                    type: boolean
                first_name:
                    type: boolean
                last_name:
                    type: boolean
                dob:
                    type: boolean
                sex:
                    type: boolean
                location:
                    type: boolean
        PatientStatus:
            type: string
            enum:
                - "cancelled"
                - "cancel-initiated"
                - "confirmed"
                - "opted-out"
                - "to-reschedule"
        PaymentCard:
            type: object
            properties:
                lastFour:
                    type: string
                brand:
                    type: string
                expiryYear:
                    type: number
                expiryMonth:
                    type: number
        PaymentError:
            type: string
            enum:
                - "Declined"
                - "Expired"
                - "CardTypeNotAccepted"
                - "IncorrectCVC"
                - "IncorrectCardNumber"
                - "GenericError"
        PaymentStatus:
            type: string
            enum:
                - "prepaid"
                - "purchased"
                - "gracePeriod"
                - "unpaid"
                - "premium"
        PaymentToken:
            type: object
            properties:
                token:
                    description: used if paymentProvider is Stripe
                    type: string
                paymentProvider:
                    description: Stripe
                    type: string
                    enum:
                        - "Stripe"
                planId:
                    description: for order creation
                    type: integer
        PendingRequest:
            type: object
            properties:
                firstName:
                    type: string
                lastName:
                    type: string
                altLastName:
                    type: string
                patientId:
                    type: string
                clinicId:
                    type: integer
                orgId:
                    type: integer
                timestamp:
                    type: string
                clinicName:
                    type: string
                date:
                    type: string
                requestId:
                    type: integer
                editable:
                    type: boolean
                status:
                    type: string
                dob:
                    type: string
                ohip:
                    type: string
                ohipVc:
                    type: string
                isBcphn:
                    type: boolean
                mrn:
                    type: string
                ssn:
                    type: string
                ipn:
                    type: string
                altId:
                    type: string
                contents:
                    $ref: '#/components/schemas/StudyRequest'
                providerName:
                    type: string
                providerLogo:
                    type: string
        Physician:
            type: object
            properties:
                id:
                    type: string
                accountId:
                    type: string
                firstName:
                    type: string
                lastName:
                    type: string
                email:
                    type: string
                isEmailVerified:
                    type: boolean
                fax:
                    type: string
                isFaxVerified:
                    type: boolean
                phone:
                    type: string
                defaultNotificationMethod:
                    $ref: '#/components/schemas/NotificationMethod'
                address:
                    type: string
                otherInfo:
                    type: string
                createdAt:
                    type: string
                    format: date-time
                updatedAt:
                    type: string
                    format: date-time
                physicianLicenses:
                    type: array
                    items:
                        $ref: '#/components/schemas/PhysicianLicense'
            required:
                - id
                - accountId
        PhysicianAccount:
            type: object
            properties:
                id:
                    type: string
                email:
                    type: string
                mainRegion:
                    type: integer
                region:
                    type: string
                createdAt:
                    type: string
                    format: date-time
                updatedAt:
                    type: string
                    format: date-time
                physicians:
                    type: array
                    items:
                        $ref: '#/components/schemas/Physician'
                permissionsMap:
                    type: object
                    additionalProperties:
                        type: array
                        items:
                            $ref: '#/components/schemas/PhysicianPermission'
            required:
                - id
        PhysicianLicense:
            type: object
            properties:
                id:
                    type: string
                physicianId:
                    type: string
                licenseType:
                    type: string
                licenseNumber:
                    type: string
                stateOrProvince:
                    type: string
                createdAt:
                    type: string
                    format: date-time
                expiryDate:
                    type: string
                    format: date-time
                updatedAt:
                    type: string
                    format: date-time
            required:
                - id
                - physicianId
        PhysicianPatientSummary:
            description: patient info for a physician
            type: object
            properties:
                id:
                    description: identifier of the patient summary, based on either patientId (for patient shares) OR patient name, dob and legacy provider id (for provider shares)
                    type: string
                patientId:
                    description: id of pockethealth account that shared records, only set for patient shares
                    type: string
                providerId:
                    description: legacy provider id of provider who shared records, only set for provider shares
                    type: integer
                orgName:
                    description: name of provider who shared records, only set for provider shares
                    type: string
                shareIds:
                    type: array
                    items:
                        description: ids of shares (or record streaming studies) shared by provider, only set for provider shares
                        type: string
                firstName:
                    description: first name of patient
                    type: string
                lastName:
                    description: last name of patient
                    type: string
                patientName:
                    $ref: '#/components/schemas/PatientName'
                dob:
                    description: date of birth of patient
                    type: string
                recordNum:
                    description: number of records shared for this patient
                    type: integer
                referringPhysicians:
                    type: array
                    items:
                        $ref: '#/components/schemas/PatientName'
                physicianAccountId:
                    description: account ID of the physician that has permissions on the study
                    type: string
                permissionGroups:
                    type: array
                    items:
                        $ref: '#/components/schemas/PhysicianPermissionGroup'
        PhysicianPermission:
            description: permission for a physician account
            type: object
            properties:
                permissionId:
                    type: integer
                permissionName:
                    type: string
                permissionDescription:
                    type: string
            required:
                - permissionId
                - permissionName
        PhysicianPermissionGroup:
            type: object
            properties:
                groupId:
                    type: integer
                    format: int64
                groupName:
                    type: string
            required:
                - groupId
        PhysicianRecordResponse:
            description: response to a record streaming retrieval request made by a physician
            type: object
            properties:
                studyUID:
                    description: DICOM identifier of study
                    type: string
                success:
                    description: indicates if the retrieval request for this study was successful
                    type: boolean
                alreadyUploaded:
                    description: indicates if the requested study is already fully available
                    type: boolean
                hasReport:
                    description: indicates if there is an uploaded report for the study
                    type: boolean
            required:
                - studyUID
                - success
                - alreadyUploaded
        PhysicianRecordUploadStatus:
            type: object
            properties:
                studyUID:
                    description: DICOM UID of study
                    type: string
                providerID:
                    description: legacy provider id, also known as orgID
                    type: integer
                    format: int64
                uuid:
                    description: unique ksuid of DICOM study object created during study upload
                    type: string
                hasReport:
                    description: boolean describing if study has uploaded report, false if there is no report or a report has not been uploaded yet
                    type: boolean
                progressPercent:
                    description: upload percentage of study - determined by how many images have been processed compared to total number of images in the study
                    type: integer
            required:
                - studyUID
                - providerID
                - uuid
                - hasReport
                - progressPercent
        PhysicianSearchQuery:
            type: object
            properties:
                firstName:
                    type: string
                lastName:
                    type: string
                dateOfBirth:
                    description: date of birth in format yyyy-mm-dd
                    type: string
                    format: date
                studyStartDate:
                    description: earliest study date in format yyyy-mm-dd
                    type: string
                    format: date
                studyEndDate:
                    description: latest study date in format yyyy-mm-dd
                    type: string
                    format: date
            required:
                - firstName
                - lastName
                - dateOfBirth
        PhysicianShareInfo:
            description: share info of an exam - containing shareId and expiry if access to exam can expire, extendedExpiry if access to exam can expire and has been extended, and eunityToken needed to view exam if access is not expired
            type: object
            properties:
                shareId:
                    type: string
                expiry:
                    type: string
                extendedExpiry:
                    type: string
                eunityToken:
                    type: string
            required:
                - shareId
        Plan:
            type: object
            properties:
                id:
                    type: integer
                name:
                    type: string
                display_name:
                    type: string
                created_at:
                    type: string
                is_active:
                    type: boolean
                is_recurring:
                    type: boolean
                amount:
                    type: integer
                period_unit:
                    type: string
                period_length:
                    type: integer
                region_id:
                    type: integer
                charge_tax:
                    type: boolean
                is_collapsed:
                    type: boolean
        Provider:
            type: object
            properties:
                providerId:
                    type: integer
                orgId:
                    type: integer
                name:
                    type: string
                address:
                    type: string
                feeAmount:
                    description: Before tax
                    type: number
                taxName:
                    type: string
                taxPercent:
                    type: string
                region:
                    type: string
                urlName:
                    type: string
        ProviderConfig:
            type: object
            properties:
                settings:
                    type: object
                    additionalProperties:
                        $ref: '#/components/schemas/ProviderConfigSetting'
        ProviderConfigSetting:
            type: object
            properties:
                showUpsell:
                    type: boolean
                showCoreUpsell:
                    type: boolean
                disabledFeatures:
                    type: array
                    items:
                        type: number
                recordStreaming:
                    type: boolean
        ProviderDestination:
            description: A Provider Destination object representing a destination within a provider's environment
            type: object
            properties:
                id:
                    description: The identifier for this provider destination
                    type: integer
                    format: int64
                label:
                    description: The local address book label from the provider
                    type: string
            required:
                - id
                - label
        ProviderLogo:
            description: Details needed to render a provider logo
            type: object
            properties:
                path:
                    type: string
                width:
                    type: integer
                    format: int64
                height:
                    type: integer
                    format: int64
            required:
                - path
                - width
                - height
        PublicReferEmailThrottle:
            type: object
            properties:
                email_throttle_number:
                    type: number
                available_number:
                    type: number
        PublicReferral:
            type: object
            properties:
                emails:
                    type: array
                    items:
                        type: string
        Record:
            type: object
            properties:
                id:
                    type: string
                name:
                    type: string
                source:
                    $ref: '#/components/schemas/RecordSource'
                recordDate:
                    type: string
                description:
                    type: string
                typeCode:
                    $ref: '#/components/schemas/RecordTypeCode'
                createdDate:
                    type: string
                filenames:
                    type: string
                tag:
                    type: string
                    maxLength: 50
                    pattern: ^[A-Za-z0-9_]*$
        RecordSource:
            type: object
            properties:
                type:
                    type: string
                description:
                    type: string
        RecordTypeCode:
            type: integer
            enum:
                - 1
                - 2
                - 3
                - 4
                - 5
                - 6
                - 7
                - 8
                - 9
                - 10
        RecordUploadStatus:
            type: object
            properties:
                orgId:
                    description: legacy provider id
                    type: integer
                examId:
                    description: DICOM UID of study
                    type: string
                uuid:
                    description: ksuid of exam object created during study upload, if available
                    type: string
                progressPercent:
                    description: upload percentage of study - determined by how many images have been processed compared to total number of images in the study
                    type: integer
                modality:
                    description: modality of the study - determined by the modality of the first series within the study
                    type: string
                examType:
                    description: type of exam - based on modality
                    type: string
                date:
                    description: date of exam, in format 'yyyy/MM/dd'
                    type: string
                    format: date
                description:
                    description: description of study
                    type: string
                orgName:
                    description: name of provider, 'Unknown' if no name is available
                    type: string
                patientId:
                    description: id of patient within pockethealth patient account that owns this study
                    type: string
                unlockStatus:
                    description: availability of a certain study to a patient, e.g. locked
                    type: string
            required:
                - orgId
                - examId
                - progressPercent
        Referral:
            type: object
            properties:
                email:
                    type: string
        RegisterData:
            type: object
            properties:
                email:
                    type: string
                password:
                    type: string
                first_name:
                    type: string
                last_name:
                    type: string
                dob:
                    description: ISO8601 format
                    type: string
                    format: date-time
                redirect_url:
                    type: string
                share_id:
                    type: string
            required:
                - email
                - password
        RejectVerifyDetails:
            type: object
            properties:
                recentExamDate:
                    type: string
                hasOlderExams:
                    type: boolean
                providerConfirmed:
                    type: boolean
                providerId:
                    type: number
                providerText:
                    type: string
                examTypes:
                    type: array
                    items:
                        type: string
                otherExamType:
                    type: string
                comments:
                    type: string
            required:
                - recentExamDate
                - hasOlderExams
                - providerConfirmed
        Report:
            type: object
            properties:
                reportId:
                    type: string
                clinicId:
                    type: integer
                clinicName:
                    type: string
                parsedTime:
                    type: string
                uploadTime:
                    type: string
                activated:
                    type: boolean
                referringPhysician:
                    type: string
                size:
                    type: integer
                inactivatedTransferId:
                    type: string
                patientName:
                    $ref: '#/components/schemas/PatientName'
                dob:
                    type: string
                sex:
                    type: string
                protocol:
                    type: string
                definitions:
                    type: boolean
        ReportInsight:
            type: object
            properties:
                organviz:
                    $ref: '#/components/schemas/OrganViz'
                has_explanation:
                    type: boolean
        Request:
            type: object
            properties:
                requestId:
                    description: Not populated on POST
                    type: integer
                firstName:
                    type: string
                lastName:
                    type: string
                altLastName:
                    type: string
                ohip:
                    type: string
                ohipvc:
                    type: string
                mrn:
                    type: string
                otherId:
                    type: string
                dob:
                    type: string
                tel:
                    type: string
                email:
                    type: string
                contents:
                    $ref: '#/components/schemas/StudyRequest'
                ssn:
                    type: string
                ipn:
                    type: string
                orgId:
                    type: integer
                providerId:
                    type: integer
                altId:
                    type: string
                comment:
                    type: string
                bcphn:
                    type: string
                patientId:
                    type: string
        RequestFormConfig:
            type: object
            properties:
                singleDate:
                    type: boolean
                recentAndComment:
                    type: boolean
                mrn:
                    type: string
                delegateConsent:
                    type: string
                examType:
                    type: string
                patientConsent:
                    type: string
                examSite:
                    type: string
                examSiteMrnPrefix:
                    type: string
                modalTypes:
                    type: array
                    items:
                        type: string
                provider:
                    $ref: '#/components/schemas/Provider'
                payment:
                    type: boolean
                altID:
                    type: string
                multilocations:
                    type: boolean
                delegate:
                    type: boolean
                reports:
                    type: boolean
                recordText:
                    type: string
                paymentText:
                    type: string
                enrollConsent:
                    type: boolean
                legal:
                    type: boolean
                legalSurcharge:
                    type: string
                ohip:
                    type: boolean
                'ipn ':
                    type: boolean
                ssn:
                    type: boolean
                bcphn:
                    type: boolean
                affiliatedText:
                    type: string
                minConsentAge:
                    type: integer
                    format: int32
                examDate:
                    type: boolean
                patientAddress:
                    type: boolean
                enrollOpt:
                    type: boolean
                base64Logo:
                    type: string
                surveyId:
                    type: integer
                showLoginLink:
                    type: boolean
                showZohoLiveChat:
                    type: boolean
                requireDelegateReview:
                    type: boolean
        RequestFormStep:
            description: Steps in the Request Form.
            type: string
            enum:
                - "PATIENT_INFO"
                - "RECENT_EXAM_INFO"
                - "FUTURE_EXAM_NOTIFICATIONS"
                - "CONSENT"
                - "PAYMENT"
                - "COMPLETED"
                - "PATIENT_INFO_CONFIRM"
        RequestStatusHistory:
            type: object
            properties:
                requestId:
                    type: string
                trackingState:
                    type: string
                    enum:
                        - "Action Needed"
                        - "Processing"
                        - "Transferring"
                        - "Ready"
                status:
                    type: string
                    enum:
                        - "UNFINALIZED"
                        - "UNDER_REVIEW"
                        - "FULFILLED"
                        - "SUPPRESS"
                        - "REJECTED"
                        - "VOID"
                        - "DELEGATE_PENDING_AUTH"
                        - "VERIFICATION_PENDING"
                        - "APPROVAL_REQUIRED"
                createdAt:
                    type: string
                    format: date-time
        RiskEligibility:
            type: object
            properties:
                breast_cancer_history:
                    $ref: '#/components/schemas/YesNoUnknown'
                has_hodgkin_lymphoma:
                    $ref: '#/components/schemas/YesNoUnknown'
                has_gene_mutations:
                    $ref: '#/components/schemas/YesNoUnknown'
                genetic_risk_breast_cancer:
                    $ref: '#/components/schemas/YesNoUnknown'
                has_current_condition:
                    $ref: '#/components/schemas/CurrentCondition'
        SOCPSODoctor:
            description: second opinion service doctor
            type: object
            properties:
                id:
                    type: integer
                first_name:
                    type: string
                last_name:
                    type: string
                phone:
                    type: string
                phone_ext:
                    type: string
                fax:
                    type: string
                address_1:
                    type: string
                address_2:
                    type: string
                address_3:
                    type: string
                address_4:
                    type: string
                address_5:
                    type: string
                cpso:
                    type: integer
                additional_locations:
                    type: string
                specialization:
                    type: string
        SOEligibility:
            description: Object that describes a response for the eligibility of an exam for second opinion
            type: object
            properties:
                eligible:
                    type: boolean
                review_status:
                    type: string
        SOReviewRequest:
            description: Object that contains all the patient entered details for requesting a second opinion.
            type: object
            properties:
                prior_exams:
                    type: array
                    items:
                        type: string
                request_patient_detail:
                    type: object
                    properties:
                        patient_name:
                            type: string
                        dob:
                            type: string
                        ohip:
                            type: string
                        ohip_version_code:
                            type: string
                        ohip_expiration_date:
                            type: string
                        phone:
                            type: string
                request_referring_detail:
                    type: object
                    properties:
                        reason:
                            type: string
                        questions:
                            type: array
                            items:
                                type: string
                        referring_physician:
                            type: object
                            properties:
                                cpso:
                                    type: integer
                                first_name:
                                    type: string
                                last_name:
                                    type: string
                                address:
                                    type: string
                                fax:
                                    type: string
                                phone:
                                    type: string
                                phone_ext:
                                    type: string
        Series:
            type: object
            properties:
                seriesId:
                    type: string
                description:
                    type: string
                instances:
                    type: array
                    items:
                        $ref: '#/components/schemas/Image'
        SetAccountOwnerRequest:
            type: object
            properties:
                patient_id:
                    type: string
        Setup:
            type: object
            properties:
                token:
                    type: string
                password:
                    type: string
        Share:
            description: Object to describe a share.
            type: object
            properties:
                shareId:
                    description: This should not be populated on POST
                    type: string
                status:
                    $ref: '#/components/schemas/FaxStatus'
                examList:
                    type: array
                    items:
                        $ref: '#/components/schemas/Exam'
                recipient:
                    description: Populate with fax number for fax shares and email address for email shares. Blank for print shares.
                    type: string
                method:
                    $ref: '#/components/schemas/ShareMethod'
                mode:
                    $ref: '#/components/schemas/ShareMode'
                expiry:
                    description: Not populated on POST
                    type: string
                active:
                    description: Not populated on POST
                    type: boolean
                date:
                    type: string
                eUnityToken:
                    type: string
                ccUser:
                    type: boolean
                extendedExpiry:
                    type: string
                healthRecords:
                    type: array
                    items:
                        $ref: '#/components/schemas/Record'
                hrPatient:
                    $ref: '#/components/schemas/HRPatient'
                containsDeletedExam:
                    type: boolean
        ShareCredentials:
            description: 'Credentials for validating a share view - only one pair should be used: shareId and pin or viewcode and dob or viewcode and dob and lastName.'
            type: object
            properties:
                shareId:
                    type: string
                pin:
                    type: string
                viewcode:
                    type: string
                dob:
                    type: string
                lastName:
                    type: string
        ShareMethod:
            description: String enum for share method. AccessPagePrint, AccessPageFax, or Email
            type: string
            enum:
                - "Email"
                - "AccessPagePrint"
                - "AccessPageFax"
                - "ZIP"
                - "ISO"
        ShareMode:
            type: string
            enum:
                - "all"
                - "multiple"
        ShortUrl:
            type: object
            properties:
                slug:
                    type: string
                original_url:
                    type: string
        StudyRequest:
            type: object
            properties:
                mode:
                    type: string
                    enum:
                        - "details"
                        - "daterange"
                allStudies:
                    type: boolean
                details:
                    type: string
                startDate:
                    type: string
                endDate:
                    type: string
                enrollment_consent:
                    type: boolean
                recent_exam_details:
                    type: object
                    properties:
                        exam_type:
                            type: string
                        exam_site:
                            type: string
                        exam_date:
                            description: Year+Month
                            type: string
                        address:
                            type: object
                            properties:
                                street_number:
                                    type: string
                                street_name:
                                    type: string
                                postal_code:
                                    type: string
                delegate:
                    type: object
                    properties:
                        firstName:
                            type: string
                        lastName:
                            type: string
                        relation:
                            type: string
                        address:
                            type: string
                        phone:
                            type: string
                        relationType:
                            type: string
                        dob:
                            type: string
                patientAddress:
                    type: string
                providerAddress:
                    type: string
        SubmitStudyNotesBody:
            description: The upload request study properties to update.
            type: object
            properties:
                uploader_notes:
                    description: Updates the study's uploader notes if not null. If empty string, clears the uploader notes.
                    type: string
                    nullable: true
                    maxLength: 50000
            required:
                - uploader_notes
        TaggedHTML:
            type: object
            properties:
                defs:
                    type: object
                report:
                    type: string
        TotalOrganViz:
            type: object
            properties:
                xray_chest:
                    type: integer
                ct_abd:
                    type: integer
        Transfer:
            type: object
            properties:
                paymentStatus:
                    $ref: '#/components/schemas/PaymentStatus'
                feeInfo:
                    $ref: '#/components/schemas/FeeInfo'
                providerName:
                    type: string
                providerId:
                    type: integer
                exams:
                    $ref: '#/components/schemas/TransferExamSummary'
        TransferExamSummary:
            type: object
            properties:
                uuid:
                    type: string
                type:
                    type: string
                examDate:
                    type: string
                description:
                    type: string
                provider:
                    type: string
                referringPhysician:
                    type: string
                reportIncluded:
                    type: boolean
        UpdateUploadRequestBody:
            description: The fields for updating upload request contact details or provider destination.
            type: object
            properties:
                provider_destination_id:
                    description: The id of the provider destination to update the upload request to.
                    type: integer
                    format: int64
                uploader_contact_name:
                    description: The contact name of the uploader.
                    type: string
                uploader_contact_method:
                    $ref: '#/components/schemas/UploadRequestUploaderContactMethod'
                uploader_contact_information:
                    description: The contact information of the format described in "uploader_contact_method"
                    type: string
        UploadFileMetadata:
            type: object
            properties:
                instanceNumber:
                    type: string
                studyDate:
                    type: string
                protocolName:
                    type: string
                modality:
                    type: string
                reportType:
                    $ref: '#/components/schemas/UploadReportType'
        UploadImage:
            description: V2 model for images to upload
            type: object
            properties:
                partname:
                    type: string
                study_id:
                    type: string
                series_id:
                    type: string
                instance_id:
                    type: string
                sha1:
                    type: string
            required:
                - partname
                - study_id
                - series_id
                - instance_id
                - sha1
        UploadInitResponse:
            type: object
            properties:
                transferId:
                    type: string
                uploadSessionId:
                    type: string
        UploadReportType:
            type: string
            enum:
                - "MHT"
                - "PDF"
        UploadRequestDestinationSetBy:
            description: Specifies whether the destination was set by the provider or the uploader.
            type: string
            enum:
                - "PROVIDER"
                - "UPLOADER"
        UploadRequestState:
            description: Represents the current state of the upload request.
            type: string
            enum:
                - "CANCELLED"
                - "COMPLETED"
                - "CREATED"
                - "DECLINED"
                - "FULFILLING"
                - "FULFILLMENT_EXPIRED"
                - "NOTIFICATION_FAILED"
                - "NOTIFIED"
        UploadRequestStudiesResponse:
            description: Response object containing a list of upload request studies.
            type: object
            properties:
                studies:
                    type: array
                    items:
                        $ref: '#/components/schemas/UploadRequestStudy'
            required:
                - studies
        UploadRequestStudy:
            description: A study that is part of an Upload Request
            type: object
            properties:
                id:
                    description: The identifier of the upload request study (this is not dicom study uid)
                    type: integer
                    format: int64
                study_instance_uid:
                    $ref: '#/components/schemas/DicomUID'
                study_description:
                    description: The DICOM study description tag value
                    type: string
                patient_name:
                    description: The DICOM patient name tag value
                    type: string
                patient_birth_date:
                    description: The DICOM patient birth date tag value
                    type: string
                modality:
                    description: The DICOM modality tag value
                    type: string
                accession_number:
                    description: The DICOM accession number tag value
                    type: string
                study_date:
                    description: The DICOM study date tag value.
                    type: string
                uploader_notes:
                    description: The notes attached by the uploader
                    type: string
                    maxLength: 50000
                instance_count:
                    description: The number of instances associated with this upload request study.
                    type: integer
                    format: int64
            required:
                - id
                - study_instance_uid
                - instance_count
        UploadRequestStudyResponse:
            description: Response object containing an upload request study.
            type: object
            properties:
                study:
                    $ref: '#/components/schemas/UploadRequestStudy'
            required:
                - study
        UploadRequestType:
            description: Identifies whether the upload request is a solicited or unsolicited request.
            type: string
            enum:
                - "SOLICITED"
                - "UNSOLICITED"
        UploadRequestUploaderContactMethod:
            type: string
            enum:
                - "PHONE_NUMBER"
                - "EMAIL"
        UploadRequestUploaderType:
            description: Identifies whether the uploader is a provider or a patient.
            type: string
            enum:
                - "PROVIDER"
                - "PATIENT"
        V2EmailUpdate:
            type: object
            properties:
                new_email:
                    type: string
        ValidationError:
            description: An error encountered when validating user-entered data
            type: object
            properties:
                field:
                    description: Machine-readable identifier for the field containing the error
                    type: string
                code:
                    description: Machine-readable validation error code (enum)
                    type: string
                message:
                    description: Human-readable validation error message
                    type: string
                additional_details:
                    summary: Arbitrary error-related details whose schema depends on the specific validation error code
                    nullable: true
            required:
                - field
                - code
        VerificationConfig:
            type: object
            properties:
                org_id:
                    type: string
                active:
                    type: boolean
                created_at:
                    type: string
                    format: date-time
                idv_link:
                    type: string
        Verify:
            type: object
            properties:
                token:
                    type: string
        YesNoUnknown:
            description: |
                YesNoUnknown:
                  * `0` - no
                  * `1` - yes
                  * `99` - unknown
            type: number
            enum:
                - 0
                - 1
                - 99
    requestBodies:
        PhysicianAccessVerificationRequest:
            content:
                application/json:
                    schema:
                        description: contains information on a study that a physician is trying to access. This can be either a shared study or a study uploaded via record streaming. Record streaming access verification happens based on studyUID and providerID. Share access verification happens based on shareID.
                        type: object
                        properties:
                            studyUID:
                                description: DICOM UID of study
                                type: string
                            providerID:
                                description: legacy provider id, also known as orgID
                                type: integer
                                format: int64
                            shareID:
                                description: id of a share - for record streaming studies, studyUID is used as shareID to ensure backwards-compatibility of some workflows
                                type: string
                        required:
                            - studyUID
                            - providerID
            required: true
        PhysicianLicenseRequest:
            content:
                application/json:
                    schema:
                        type: object
                        properties:
                            licenseType:
                                type: string
                            licenseNo:
                                type: string
                            stateOrProvince:
                                type: string
                            expiryDate:
                                type: string
                                format: date-time
        PhysicianRecordRequest:
            content:
                application/json:
                    schema:
                        type: object
                        properties:
                            providerId:
                                type: integer
                            studyRequests:
                                type: array
                                items:
                                    type: object
                                    properties:
                                        studyUID:
                                            description: DICOM UID of study that is being requestd
                                            type: string
                                        patientId:
                                            description: MRN of patient associated with study
                                            type: string
                                        issuer:
                                            description: issuer of patient MRN
                                            type: string
                                        patientFirstName:
                                            description: first name of patient
                                            type: string
                                        patientLastName:
                                            description: last name of patient
                                            type: string
                                        patientBirthDate:
                                            description: date of birth name of patient
                                            type: string
                                    required:
                                        - studyUID
                                        - patientId
                        required:
                            - providerId
                            - studyRequests
        PhysicianSearchRequest:
            content:
                application/json:
                    schema:
                        type: object
                        properties:
                            providerId:
                                type: integer
                            query:
                                $ref: '#/components/schemas/PhysicianSearchQuery'
                        required:
                            - providerId
                            - query
    securitySchemes:
        jwtBearer:
            type: http
            scheme: bearer
            flows: {}
        phSignature:
            type: apiKey
            description: authentication used for requests sent from RegionRouter. It is a signature of the form ES256(SHA256(requestBody))
            name: X-PH-Signature
            in: header
            flows: {}
