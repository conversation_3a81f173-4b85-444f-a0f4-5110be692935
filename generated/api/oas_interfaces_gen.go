// Code generated by ogen, DO NOT EDIT.
package api

type AuthenticateUploadRequestV1Res interface {
	authenticateUploadRequestV1Res()
}

type CreateUploadRequestV1Res interface {
	createUploadRequestV1Res()
}

type DeclineUploadRequestV1Res interface {
	declineUploadRequestV1Res()
}

type DeleteInternalPatientProfilesRes interface {
	deleteInternalPatientProfilesRes()
}

type DeletePatientProfileRes interface {
	deletePatientProfileRes()
}

type DeleteTransfersTransferIdRes interface {
	deleteTransfersTransferIdRes()
}

type DeleteUploadRequestStudyV1Res interface {
	deleteUploadRequestStudyV1Res()
}

type DeleteUsersLogoutRes interface {
	deleteUsersLogoutRes()
}

type DeleteV2HealthrecordsPatientIdRecordsRecordIdRes interface {
	deleteV2HealthrecordsPatientIdRecordsRecordIdRes()
}

type DeleteV2TransfersTransferIdRes interface {
	deleteV2TransfersTransferIdRes()
}

type GetAccountStateRes interface {
	getAccountStateRes()
}

type GetImageByIDRes interface {
	getImageByIDRes()
}

type GetImageMetadataByIDRes interface {
	getImageMetadataByIDRes()
}

type GetOrganVisualizationByExamIDRes interface {
	getOrganVisualizationByExamIDRes()
}

type GetPatientProfilesIDRes interface {
	getPatientProfilesIDRes()
}

type GetPatientProfilesRes interface {
	getPatientProfilesRes()
}

type GetPhysicianAccountEunityTokenRes interface {
	getPhysicianAccountEunityTokenRes()
}

type GetPhysicianAccountPatientsExamsRes interface {
	getPhysicianAccountPatientsExamsRes()
}

type GetPhysicianAccountPatientsRes interface {
	getPhysicianAccountPatientsRes()
}

type GetPhysicianAccountRes interface {
	getPhysicianAccountRes()
}

type GetPhysicianAccountStudyStatesRes interface {
	getPhysicianAccountStudyStatesRes()
}

type GetProviderByIdRes interface {
	getProviderByIdRes()
}

type GetProviderConfigRes interface {
	getProviderConfigRes()
}

type GetProviderDetailsV1Res interface {
	getProviderDetailsV1Res()
}

type GetProviderFormConfigRes interface {
	getProviderFormConfigRes()
}

type GetProvidersRes interface {
	getProvidersRes()
}

type GetReferTokenRes interface {
	getReferTokenRes()
}

type GetReportByIdRes interface {
	getReportByIdRes()
}

type GetReportsReportIdInsightsQuestionsRes interface {
	getReportsReportIdInsightsQuestionsRes()
}

type GetReportsReportIdMetadataRes interface {
	getReportsReportIdMetadataRes()
}

type GetReportsReportIdOrganVisualiationRes interface {
	getReportsReportIdOrganVisualiationRes()
}

type GetReportsRes interface {
	getReportsRes()
}

type GetRequestsRes interface {
	getRequestsRes()
}

type GetShareExamsByshareIdV2Res interface {
	getShareExamsByshareIdV2Res()
}

type GetSharesByIdRes interface {
	getSharesByIdRes()
}

type GetSharesHealthrecordsByIdRes interface {
	getSharesHealthrecordsByIdRes()
}

type GetSharesRes interface {
	getSharesRes()
}

type GetShortUrlsRes interface {
	getShortUrlsRes()
}

type GetTransfersTransferIdChallengeRes interface {
	getTransfersTransferIdChallengeRes()
}

type GetTransfersTransferIdRes interface {
	getTransfersTransferIdRes()
}

type GetUploadRequestStudyByIDV1Res interface {
	getUploadRequestStudyByIDV1Res()
}

type GetUploadRequestV1Res interface {
	getUploadRequestV1Res()
}

type GetUserExamByIdRes interface {
	getUserExamByIdRes()
}

type GetUserExamsRes interface {
	getUserExamsRes()
}

type GetUserNotificationsRes interface {
	getUserNotificationsRes()
}

type GetUserRequestConsentRes interface {
	getUserRequestConsentRes()
}

type GetUsersExamsLookupRes interface {
	getUsersExamsLookupRes()
}

type GetUsersExamsSizeRes interface {
	getUsersExamsSizeRes()
}

type GetUsersRolloutFlagRes interface {
	getUsersRolloutFlagRes()
}

type GetUsersSettingsRes interface {
	getUsersSettingsRes()
}

type GetV1AppointmentsRes interface {
	getV1AppointmentsRes()
}

type GetV1ExpertreviewPaymentprovidersRes interface {
	getV1ExpertreviewPaymentprovidersRes()
}

type GetV1PromoCodeRes interface {
	getV1PromoCodeRes()
}

type GetV1ReportsIDTaggedhtmlRes interface {
	getV1ReportsIDTaggedhtmlRes()
}

type GetV1ReportsReportIdInsightsFollowupRes interface {
	getV1ReportsReportIdInsightsFollowupRes()
}

type GetV1RequestsIncompleteIncompleteRequestIdEmailRes interface {
	getV1RequestsIncompleteIncompleteRequestIdEmailRes()
}

type GetV1RequestsIncompleteIncompleteRequestIdRes interface {
	getV1RequestsIncompleteIncompleteRequestIdRes()
}

type GetV1RequestsVerificationConfigurationRes interface {
	getV1RequestsVerificationConfigurationRes()
}

type GetV2HealthrecordsMychartSearchRes interface {
	getV2HealthrecordsMychartSearchRes()
}

type GetV2HealthrecordsPatientIdRecordsRecordIdRes interface {
	getV2HealthrecordsPatientIdRecordsRecordIdRes()
}

type GetV2HealthrecordsPatientIdRecordsRecordIdThumbnailRes interface {
	getV2HealthrecordsPatientIdRecordsRecordIdThumbnailRes()
}

type GetV2HealthrecordsPatientIdRecordsRecordTypeRes interface {
	getV2HealthrecordsPatientIdRecordsRecordTypeRes()
}

type GetV2HealthrecordsPatientIdUploadRes interface {
	getV2HealthrecordsPatientIdUploadRes()
}

type GetV2OrdersRes interface {
	getV2OrdersRes()
}

type GetV2PatientIdRes interface {
	getV2PatientIdRes()
}

type GetV2ProvidersRes interface {
	getV2ProvidersRes()
}

type GetV2RhoEligibleExamIdRes interface {
	getV2RhoEligibleExamIdRes()
}

type GetV2SecondopiniontDoctorsSearchRes interface {
	getV2SecondopiniontDoctorsSearchRes()
}

type GetV2UsersExamsLookupRes interface {
	getV2UsersExamsLookupRes()
}

type ListUploadRequestStudiesV1Res interface {
	listUploadRequestStudiesV1Res()
}

type PatchPatientProfileRes interface {
	patchPatientProfileRes()
}

type PatchPhysicianAccountsRes interface {
	patchPhysicianAccountsRes()
}

type PatchRequestsIDCancelRes interface {
	patchRequestsIDCancelRes()
}

type PatchUsersDeactivateAccountRes interface {
	patchUsersDeactivateAccountRes()
}

type PatchUsersSetAccountOwnerRes interface {
	patchUsersSetAccountOwnerRes()
}

type PatchV1RequestsIncompleteIncompleteRequestIdRes interface {
	patchV1RequestsIncompleteIncompleteRequestIdRes()
}

type PostEmailVerificationRes interface {
	postEmailVerificationRes()
}

type PostPatientProfilesRes interface {
	postPatientProfilesRes()
}

type PostPhysicianAccountStudyAccessRes interface {
	postPhysicianAccountStudyAccessRes()
}

type PostPhysicianAccountsLoginRes interface {
	postPhysicianAccountsLoginRes()
}

type PostPhysicianAccountsLogoutRes interface {
	postPhysicianAccountsLogoutRes()
}

type PostPhysicianAccountsRequestRes interface {
	postPhysicianAccountsRequestRes()
}

type PostPhysicianAccountsRes interface {
	postPhysicianAccountsRes()
}

type PostPhysicianAccountsResetRes interface {
	postPhysicianAccountsResetRes()
}

type PostPhysicianAccountsSearchRes interface {
	postPhysicianAccountsSearchRes()
}

type PostPhysicianAccountsVerifyRes interface {
	postPhysicianAccountsVerifyRes()
}

type PostPhysicianLicenseRes interface {
	postPhysicianLicenseRes()
}

type PostProvidersConsentsConsentidRes interface {
	postProvidersConsentsConsentidRes()
}

type PostProvidersConsentsConsentidUnverifiedRes interface {
	postProvidersConsentsConsentidUnverifiedRes()
}

type PostProvidersConsentsEmailVerificationRes interface {
	postProvidersConsentsEmailVerificationRes()
}

type PostReferTokenRes interface {
	postReferTokenRes()
}

type PostReportViewsRes interface {
	postReportViewsRes()
}

type PostRequestsIDResubmitRes interface {
	postRequestsIDResubmitRes()
}

type PostRequestsRes interface {
	postRequestsRes()
}

type PostRequestsUphRes interface {
	postRequestsUphRes()
}

type PostReshareRes interface {
	postReshareRes()
}

type PostSharesRes interface {
	postSharesRes()
}

type PostSharesValidateRes interface {
	postSharesValidateRes()
}

type PostShortUrlsRes interface {
	postShortUrlsRes()
}

type PostTransferPaymentRes interface {
	postTransferPaymentRes()
}

type PostTransfersTransferIdChallengeRes interface {
	postTransfersTransferIdChallengeRes()
}

type PostTransfersTransferIdReactivateRes interface {
	postTransfersTransferIdReactivateRes()
}

type PostUserNotificationRes interface {
	postUserNotificationRes()
}

type PostUsersLockAccountRes interface {
	postUsersLockAccountRes()
}

type PostUsersLoginSSORes interface {
	postUsersLoginSSORes()
}

type PostUsersReferralRes interface {
	postUsersReferralRes()
}

type PostUsersSetupRes interface {
	postUsersSetupRes()
}

type PostUsersUserIdRes interface {
	postUsersUserIdRes()
}

type PostUsersVerifyRes interface {
	postUsersVerifyRes()
}

type PostV1AppointmentsPatientstatusRes interface {
	postV1AppointmentsPatientstatusRes()
}

type PostV1RequestsIncompleteIncompleteRequestIdEmailStatusRes interface {
	postV1RequestsIncompleteIncompleteRequestIdEmailStatusRes()
}

type PostV1RequestsIncompleteIncompleteRequestIdVerifyRes interface {
	postV1RequestsIncompleteIncompleteRequestIdVerifyRes()
}

type PostV1RequestsIncompleteRes interface {
	postV1RequestsIncompleteRes()
}

type PostV1SecondopinionReviewsRes interface {
	postV1SecondopinionReviewsRes()
}

type PostV1SharesShareIdDltokenRes interface {
	postV1SharesShareIdDltokenRes()
}

type PostV2HealthrecordsMychartPatientIdRes interface {
	postV2HealthrecordsMychartPatientIdRes()
}

type PostV2HealthrecordsPatientIdGailRes interface {
	postV2HealthrecordsPatientIdGailRes()
}

type PostV2HealthrecordsPatientIdRes interface {
	postV2HealthrecordsPatientIdRes()
}

type PostV2HealthrecordsVerifyRes interface {
	postV2HealthrecordsVerifyRes()
}

type PostV2OrdersRes interface {
	postV2OrdersRes()
}

type PostV2RhoRequestRes interface {
	postV2RhoRequestRes()
}

type PostV2SecondopinionEligiblePriorsRes interface {
	postV2SecondopinionEligiblePriorsRes()
}

type PostV2TransfersRes interface {
	postV2TransfersRes()
}

type PostV2TransfersTransferIdFileRes interface {
	postV2TransfersTransferIdFileRes()
}

type PostV2TransfersTransferIdFinalizeRes interface {
	postV2TransfersTransferIdFinalizeRes()
}

type PostV2TransfersTransferIdReportdcmRes interface {
	postV2TransfersTransferIdReportdcmRes()
}

type PostV2UsersResetpasswordRes interface {
	postV2UsersResetpasswordRes()
}

type PostVerifyDobRes interface {
	postVerifyDobRes()
}

type PutNotificationReadByIdRes interface {
	putNotificationReadByIdRes()
}

type PutPhysicianAccountsShareExtendRes interface {
	putPhysicianAccountsShareExtendRes()
}

type PutRevokeShareRes interface {
	putRevokeShareRes()
}

type PutShareExtendByshareIdRes interface {
	putShareExtendByshareIdRes()
}

type PutUsersSubscriptionToggleautorenewRes interface {
	putUsersSubscriptionToggleautorenewRes()
}

type PutUsersUpdateEmailRes interface {
	putUsersUpdateEmailRes()
}

type PutV2HealthrecordsPatientIdRecordsRecordIdRes interface {
	putV2HealthrecordsPatientIdRecordsRecordIdRes()
}

type PutV2OrdersPaymentDetailsRes interface {
	putV2OrdersPaymentDetailsRes()
}

type SubmitUploadRequestV1Res interface {
	submitUploadRequestV1Res()
}

type UpdateUploadRequestStudyV1Res interface {
	updateUploadRequestStudyV1Res()
}

type UpdateUploadRequestV1Res interface {
	updateUploadRequestV1Res()
}

type UpdateUserSettingsRes interface {
	updateUserSettingsRes()
}

type UploadRequestUploadInstanceV1Res interface {
	uploadRequestUploadInstanceV1Res()
}

type V1MeddreamGenerateExamUUIDGetRes interface {
	v1MeddreamGenerateExamUUIDGetRes()
}

type V1RecordsUploadStatusGetRes interface {
	v1RecordsUploadStatusGetRes()
}

type V1UsersExamsEligibleInsightsGetRes interface {
	v1UsersExamsEligibleInsightsGetRes()
}

type V1UsersExamsExamuuidThumbnailGetRes interface {
	v1UsersExamsExamuuidThumbnailGetRes()
}

type V1UsersNotificationsNotificationIdDeleteRes interface {
	v1UsersNotificationsNotificationIdDeleteRes()
}

type V1UsersReportinsightsGetRes interface {
	v1UsersReportinsightsGetRes()
}

type V2PatientsIncompleteNotificationPostRes interface {
	v2PatientsIncompleteNotificationPostRes()
}

type V2PatientsPatientIDValidGetRes interface {
	v2PatientsPatientIDValidGetRes()
}

type V2PatientsValidGetRes interface {
	v2PatientsValidGetRes()
}

type V2RequestsCreatePostRes interface {
	v2RequestsCreatePostRes()
}

type V2RequestsRidStatusHistoryPostRes interface {
	v2RequestsRidStatusHistoryPostRes()
}

type V2SecondopinionPatientEligibilityPatientIdProgramNamePutRes interface {
	v2SecondopinionPatientEligibilityPatientIdProgramNamePutRes()
}

type V2SecondopinionPatientEligibilityPostRes interface {
	v2SecondopinionPatientEligibilityPostRes()
}

type V2SecondopinionPatientEligibilityProgramNamePostRes interface {
	v2SecondopinionPatientEligibilityProgramNamePostRes()
}

type VerifyPhysicianNotificationMethodRes interface {
	verifyPhysicianNotificationMethodRes()
}
