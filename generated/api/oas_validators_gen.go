// Code generated by ogen, DO NOT EDIT.

package api

import (
	"fmt"

	"github.com/go-faster/errors"

	"github.com/ogen-go/ogen/validate"
)

func (s *AnalysisEligibility) Validate() error {
	if s == nil {
		return validate.ErrNilPointer
	}

	var failures []validate.FieldError
	if err := func() error {
		if value, ok := s.Status.Get(); ok {
			if err := func() error {
				if err := value.Validate(); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return err
			}
		}
		return nil
	}(); err != nil {
		failures = append(failures, validate.FieldError{
			Name:  "status",
			Error: err,
		})
	}
	if len(failures) > 0 {
		return &validate.Error{Fields: failures}
	}
	return nil
}

func (s AnalysisEligibilityStatus) Validate() error {
	switch s {
	case "not started":
		return nil
	case "submitted":
		return nil
	case "in progress":
		return nil
	case "rejected":
		return nil
	case "completed":
		return nil
	case "blocked":
		return nil
	case "error":
		return nil
	case "unknown status":
		return nil
	default:
		return errors.Errorf("invalid value: %v", s)
	}
}

func (s *AppointmentDetails) Validate() error {
	if s == nil {
		return validate.ErrNilPointer
	}

	var failures []validate.FieldError
	if err := func() error {
		if err := s.PatientStatus.Validate(); err != nil {
			return err
		}
		return nil
	}(); err != nil {
		failures = append(failures, validate.FieldError{
			Name:  "patient_status",
			Error: err,
		})
	}
	if err := func() error {
		if s.Appointments == nil {
			return errors.New("nil is invalid value")
		}
		return nil
	}(); err != nil {
		failures = append(failures, validate.FieldError{
			Name:  "appointments",
			Error: err,
		})
	}
	if len(failures) > 0 {
		return &validate.Error{Fields: failures}
	}
	return nil
}

func (s *AppointmentReminderPatientStatusUpdate) Validate() error {
	if s == nil {
		return validate.ErrNilPointer
	}

	var failures []validate.FieldError
	if err := func() error {
		if err := s.PatientStatus.Validate(); err != nil {
			return err
		}
		return nil
	}(); err != nil {
		failures = append(failures, validate.FieldError{
			Name:  "patient_status",
			Error: err,
		})
	}
	if len(failures) > 0 {
		return &validate.Error{Fields: failures}
	}
	return nil
}

func (s *AuthenticateUploadRequestBody) Validate() error {
	if s == nil {
		return validate.ErrNilPointer
	}

	var failures []validate.FieldError
	if err := func() error {
		if err := (validate.String{
			MinLength:    0,
			MinLengthSet: false,
			MaxLength:    0,
			MaxLengthSet: false,
			Email:        false,
			Hostname:     false,
			Regex:        regexMap["^[a-zA-Z0-9]{9}$"],
		}).Validate(string(s.SecurityCode)); err != nil {
			return errors.Wrap(err, "string")
		}
		return nil
	}(); err != nil {
		failures = append(failures, validate.FieldError{
			Name:  "security_code",
			Error: err,
		})
	}
	if len(failures) > 0 {
		return &validate.Error{Fields: failures}
	}
	return nil
}

func (s *BreastBiopsyFactor) Validate() error {
	if s == nil {
		return validate.ErrNilPointer
	}

	var failures []validate.FieldError
	if err := func() error {
		if value, ok := s.Exists.Get(); ok {
			if err := func() error {
				if err := value.Validate(); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return err
			}
		}
		return nil
	}(); err != nil {
		failures = append(failures, validate.FieldError{
			Name:  "exists",
			Error: err,
		})
	}
	if err := func() error {
		if value, ok := s.BenignBiopsies.Get(); ok {
			if err := func() error {
				if err := value.Validate(); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return err
			}
		}
		return nil
	}(); err != nil {
		failures = append(failures, validate.FieldError{
			Name:  "benign_biopsies",
			Error: err,
		})
	}
	if err := func() error {
		if value, ok := s.AtypicalHyperplasia.Get(); ok {
			if err := func() error {
				if err := value.Validate(); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return err
			}
		}
		return nil
	}(); err != nil {
		failures = append(failures, validate.FieldError{
			Name:  "atypical_hyperplasia",
			Error: err,
		})
	}
	if len(failures) > 0 {
		return &validate.Error{Fields: failures}
	}
	return nil
}

func (s *CreateRequestMultipart) Validate() error {
	if s == nil {
		return validate.ErrNilPointer
	}

	var failures []validate.FieldError
	if err := func() error {
		if value, ok := s.RequestDetails.Get(); ok {
			if err := func() error {
				if err := value.Validate(); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return err
			}
		}
		return nil
	}(); err != nil {
		failures = append(failures, validate.FieldError{
			Name:  "requestDetails",
			Error: err,
		})
	}
	if err := func() error {
		if value, ok := s.PaymentToken.Get(); ok {
			if err := func() error {
				if err := value.Validate(); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return err
			}
		}
		return nil
	}(); err != nil {
		failures = append(failures, validate.FieldError{
			Name:  "paymentToken",
			Error: err,
		})
	}
	if len(failures) > 0 {
		return &validate.Error{Fields: failures}
	}
	return nil
}

func (s *CreateUploadRequestBody) Validate() error {
	if s == nil {
		return validate.ErrNilPointer
	}

	var failures []validate.FieldError
	if err := func() error {
		if err := s.UploaderType.Validate(); err != nil {
			return err
		}
		return nil
	}(); err != nil {
		failures = append(failures, validate.FieldError{
			Name:  "uploader_type",
			Error: err,
		})
	}
	if err := func() error {
		if err := s.ContactMethod.Validate(); err != nil {
			return err
		}
		return nil
	}(); err != nil {
		failures = append(failures, validate.FieldError{
			Name:  "contact_method",
			Error: err,
		})
	}
	if len(failures) > 0 {
		return &validate.Error{Fields: failures}
	}
	return nil
}

func (s CurrentCondition) Validate() error {
	switch s {
	case "DCIS":
		return nil
	case "LCIS":
		return nil
	case "0":
		return nil
	default:
		return errors.Errorf("invalid value: %v", s)
	}
}

func (s *DeclineUploadRequestBody) Validate() error {
	if s == nil {
		return validate.ErrNilPointer
	}

	var failures []validate.FieldError
	if err := func() error {
		if err := (validate.String{
			MinLength:    0,
			MinLengthSet: false,
			MaxLength:    0,
			MaxLengthSet: false,
			Email:        false,
			Hostname:     false,
			Regex:        regexMap["^[a-zA-Z0-9]{9}$"],
		}).Validate(string(s.SecurityCode)); err != nil {
			return errors.Wrap(err, "string")
		}
		return nil
	}(); err != nil {
		failures = append(failures, validate.FieldError{
			Name:  "security_code",
			Error: err,
		})
	}
	if err := func() error {
		if err := (validate.String{
			MinLength:    1,
			MinLengthSet: true,
			MaxLength:    0,
			MaxLengthSet: false,
			Email:        false,
			Hostname:     false,
			Regex:        nil,
		}).Validate(string(s.DeclineReason)); err != nil {
			return errors.Wrap(err, "string")
		}
		return nil
	}(); err != nil {
		failures = append(failures, validate.FieldError{
			Name:  "decline_reason",
			Error: err,
		})
	}
	if len(failures) > 0 {
		return &validate.Error{Fields: failures}
	}
	return nil
}

func (s DicomUID) Validate() error {
	alias := (string)(s)
	if err := (validate.String{
		MinLength:    1,
		MinLengthSet: true,
		MaxLength:    64,
		MaxLengthSet: true,
		Email:        false,
		Hostname:     false,
		Regex:        regexMap["^[0-9.]*$"],
	}).Validate(string(alias)); err != nil {
		return errors.Wrap(err, "string")
	}
	return nil
}

func (s FaxStatus) Validate() error {
	switch s {
	case "delivered":
		return nil
	case "failed":
		return nil
	case "no-answer":
		return nil
	case "busy":
		return nil
	case "canceled":
		return nil
	default:
		return errors.Errorf("invalid value: %v", s)
	}
}

func (s *FeeInfo) Validate() error {
	if s == nil {
		return validate.ErrNilPointer
	}

	var failures []validate.FieldError
	if err := func() error {
		if value, ok := s.FeeAmount.Get(); ok {
			if err := func() error {
				if err := (validate.Float{}).Validate(float64(value)); err != nil {
					return errors.Wrap(err, "float")
				}
				return nil
			}(); err != nil {
				return err
			}
		}
		return nil
	}(); err != nil {
		failures = append(failures, validate.FieldError{
			Name:  "feeAmount",
			Error: err,
		})
	}
	if err := func() error {
		if value, ok := s.TaxPercent.Get(); ok {
			if err := func() error {
				if err := (validate.Float{}).Validate(float64(value)); err != nil {
					return errors.Wrap(err, "float")
				}
				return nil
			}(); err != nil {
				return err
			}
		}
		return nil
	}(); err != nil {
		failures = append(failures, validate.FieldError{
			Name:  "taxPercent",
			Error: err,
		})
	}
	if len(failures) > 0 {
		return &validate.Error{Fields: failures}
	}
	return nil
}

func (s *GailQuestionnaireResponse) Validate() error {
	if s == nil {
		return validate.ErrNilPointer
	}

	var failures []validate.FieldError
	if err := func() error {
		if err := s.EligibilityCheck.Validate(); err != nil {
			return err
		}
		return nil
	}(); err != nil {
		failures = append(failures, validate.FieldError{
			Name:  "eligibility_check",
			Error: err,
		})
	}
	if err := func() error {
		if err := s.Ethnicity.Validate(); err != nil {
			return err
		}
		return nil
	}(); err != nil {
		failures = append(failures, validate.FieldError{
			Name:  "ethnicity",
			Error: err,
		})
	}
	if err := func() error {
		if err := s.SubEthnicity.Validate(); err != nil {
			return err
		}
		return nil
	}(); err != nil {
		failures = append(failures, validate.FieldError{
			Name:  "sub_ethnicity",
			Error: err,
		})
	}
	if err := func() error {
		if err := (validate.Float{}).Validate(float64(s.AgeMen)); err != nil {
			return errors.Wrap(err, "float")
		}
		return nil
	}(); err != nil {
		failures = append(failures, validate.FieldError{
			Name:  "age_men",
			Error: err,
		})
	}
	if err := func() error {
		if err := (validate.Float{}).Validate(float64(s.AgeBirth)); err != nil {
			return errors.Wrap(err, "float")
		}
		return nil
	}(); err != nil {
		failures = append(failures, validate.FieldError{
			Name:  "age_birth",
			Error: err,
		})
	}
	if err := func() error {
		if err := s.FirstDegreeRelatives.Validate(); err != nil {
			return err
		}
		return nil
	}(); err != nil {
		failures = append(failures, validate.FieldError{
			Name:  "first_degree_relatives",
			Error: err,
		})
	}
	if err := func() error {
		if err := s.BreastBiopsy.Validate(); err != nil {
			return err
		}
		return nil
	}(); err != nil {
		failures = append(failures, validate.FieldError{
			Name:  "breast_biopsy",
			Error: err,
		})
	}
	if len(failures) > 0 {
		return &validate.Error{Fields: failures}
	}
	return nil
}

func (s GailQuestionnaireResponseEthnicity) Validate() error {
	switch s {
	case "Wh":
		return nil
	case "AA":
		return nil
	case "H":
		return nil
	case "NA":
		return nil
	case "A":
		return nil
	default:
		return errors.Errorf("invalid value: %v", s)
	}
}

func (s GailQuestionnaireResponseSubEthnicity) Validate() error {
	switch s {
	case "HU":
		return nil
	case "HF":
		return nil
	case "Ch":
		return nil
	case "Ja":
		return nil
	case "Fi":
		return nil
	case "Hw":
		return nil
	case "oP":
		return nil
	case "oA":
		return nil
	default:
		return errors.Errorf("invalid value: %v", s)
	}
}

func (s *GailRiskScore) Validate() error {
	if s == nil {
		return validate.ErrNilPointer
	}

	var failures []validate.FieldError
	if err := func() error {
		if value, ok := s.RiskFiveYears.Get(); ok {
			if err := func() error {
				if err := (validate.Float{}).Validate(float64(value)); err != nil {
					return errors.Wrap(err, "float")
				}
				return nil
			}(); err != nil {
				return err
			}
		}
		return nil
	}(); err != nil {
		failures = append(failures, validate.FieldError{
			Name:  "risk_five_years",
			Error: err,
		})
	}
	if err := func() error {
		if value, ok := s.RiskLifetime.Get(); ok {
			if err := func() error {
				if err := (validate.Float{}).Validate(float64(value)); err != nil {
					return errors.Wrap(err, "float")
				}
				return nil
			}(); err != nil {
				return err
			}
		}
		return nil
	}(); err != nil {
		failures = append(failures, validate.FieldError{
			Name:  "risk_lifetime",
			Error: err,
		})
	}
	if len(failures) > 0 {
		return &validate.Error{Fields: failures}
	}
	return nil
}

func (s GetImageByIDAccept) Validate() error {
	switch s {
	case "application/dicom":
		return nil
	case "image/png":
		return nil
	default:
		return errors.Errorf("invalid value: %v", s)
	}
}

func (s GetPatientProfilesOKApplicationJSON) Validate() error {
	alias := ([]Patient)(s)
	if alias == nil {
		return errors.New("nil is invalid value")
	}
	return nil
}

func (s *GetPhysicianAccountPatientsExamsOK) Validate() error {
	if s == nil {
		return validate.ErrNilPointer
	}

	var failures []validate.FieldError
	if err := func() error {
		var failures []validate.FieldError
		for i, elem := range s.Hrs {
			if err := func() error {
				if err := elem.Validate(); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				failures = append(failures, validate.FieldError{
					Name:  fmt.Sprintf("[%d]", i),
					Error: err,
				})
			}
		}
		if len(failures) > 0 {
			return &validate.Error{Fields: failures}
		}
		return nil
	}(); err != nil {
		failures = append(failures, validate.FieldError{
			Name:  "hrs",
			Error: err,
		})
	}
	if len(failures) > 0 {
		return &validate.Error{Fields: failures}
	}
	return nil
}

func (s GetPhysicianAccountPatientsOKApplicationJSON) Validate() error {
	alias := ([]PhysicianPatientSummary)(s)
	if alias == nil {
		return errors.New("nil is invalid value")
	}
	return nil
}

func (s GetPhysicianAccountStudyStatesOKApplicationJSON) Validate() error {
	alias := ([]PhysicianRecordUploadStatus)(s)
	if alias == nil {
		return errors.New("nil is invalid value")
	}
	return nil
}

func (s *GetProviderDetailsResponse) Validate() error {
	if s == nil {
		return validate.ErrNilPointer
	}

	var failures []validate.FieldError
	if err := func() error {
		if s.Destinations == nil {
			return errors.New("nil is invalid value")
		}
		return nil
	}(); err != nil {
		failures = append(failures, validate.FieldError{
			Name:  "destinations",
			Error: err,
		})
	}
	if len(failures) > 0 {
		return &validate.Error{Fields: failures}
	}
	return nil
}

func (s GetProvidersOKApplicationJSON) Validate() error {
	alias := ([]Provider)(s)
	if alias == nil {
		return errors.New("nil is invalid value")
	}
	var failures []validate.FieldError
	for i, elem := range alias {
		if err := func() error {
			if err := elem.Validate(); err != nil {
				return err
			}
			return nil
		}(); err != nil {
			failures = append(failures, validate.FieldError{
				Name:  fmt.Sprintf("[%d]", i),
				Error: err,
			})
		}
	}
	if len(failures) > 0 {
		return &validate.Error{Fields: failures}
	}
	return nil
}

func (s GetReportByIdAccept) Validate() error {
	switch s {
	case "application/pdf":
		return nil
	case "image/png":
		return nil
	case "text/html":
		return nil
	default:
		return errors.Errorf("invalid value: %v", s)
	}
}

func (s GetReportsOKApplicationJSON) Validate() error {
	alias := ([]Report)(s)
	if alias == nil {
		return errors.New("nil is invalid value")
	}
	return nil
}

func (s GetRequestsOKApplicationJSON) Validate() error {
	alias := ([]PendingRequest)(s)
	if alias == nil {
		return errors.New("nil is invalid value")
	}
	var failures []validate.FieldError
	for i, elem := range alias {
		if err := func() error {
			if err := elem.Validate(); err != nil {
				return err
			}
			return nil
		}(); err != nil {
			failures = append(failures, validate.FieldError{
				Name:  fmt.Sprintf("[%d]", i),
				Error: err,
			})
		}
	}
	if len(failures) > 0 {
		return &validate.Error{Fields: failures}
	}
	return nil
}

func (s *GetShareExamsByshareIdV2OK) Validate() error {
	if s == nil {
		return validate.ErrNilPointer
	}

	var failures []validate.FieldError
	if err := func() error {
		var failures []validate.FieldError
		for i, elem := range s.Hrs {
			if err := func() error {
				if err := elem.Validate(); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				failures = append(failures, validate.FieldError{
					Name:  fmt.Sprintf("[%d]", i),
					Error: err,
				})
			}
		}
		if len(failures) > 0 {
			return &validate.Error{Fields: failures}
		}
		return nil
	}(); err != nil {
		failures = append(failures, validate.FieldError{
			Name:  "hrs",
			Error: err,
		})
	}
	if len(failures) > 0 {
		return &validate.Error{Fields: failures}
	}
	return nil
}

func (s *GetUploadRequestResponse) Validate() error {
	if s == nil {
		return validate.ErrNilPointer
	}

	var failures []validate.FieldError
	if err := func() error {
		if value, ok := s.ProviderDestinationSetBy.Get(); ok {
			if err := func() error {
				if err := value.Validate(); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return err
			}
		}
		return nil
	}(); err != nil {
		failures = append(failures, validate.FieldError{
			Name:  "provider_destination_set_by",
			Error: err,
		})
	}
	if err := func() error {
		if err := s.RequestType.Validate(); err != nil {
			return err
		}
		return nil
	}(); err != nil {
		failures = append(failures, validate.FieldError{
			Name:  "request_type",
			Error: err,
		})
	}
	if err := func() error {
		if err := s.State.Validate(); err != nil {
			return err
		}
		return nil
	}(); err != nil {
		failures = append(failures, validate.FieldError{
			Name:  "state",
			Error: err,
		})
	}
	if err := func() error {
		if value, ok := s.UploaderType.Get(); ok {
			if err := func() error {
				if err := value.Validate(); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return err
			}
		}
		return nil
	}(); err != nil {
		failures = append(failures, validate.FieldError{
			Name:  "uploader_type",
			Error: err,
		})
	}
	if err := func() error {
		if value, ok := s.UploaderContactMethod.Get(); ok {
			if err := func() error {
				if err := value.Validate(); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return err
			}
		}
		return nil
	}(); err != nil {
		failures = append(failures, validate.FieldError{
			Name:  "uploader_contact_method",
			Error: err,
		})
	}
	if len(failures) > 0 {
		return &validate.Error{Fields: failures}
	}
	return nil
}

func (s GetUserExamsOKApplicationJSON) Validate() error {
	alias := ([]Exam)(s)
	if alias == nil {
		return errors.New("nil is invalid value")
	}
	return nil
}

func (s GetUsersExamsSizeOKApplicationJSON) Validate() error {
	alias := (float64)(s)
	if err := (validate.Float{}).Validate(float64(alias)); err != nil {
		return errors.Wrap(err, "float")
	}
	return nil
}

func (s GetV1ExpertreviewPaymentprovidersOKApplicationJSON) Validate() error {
	alias := ([]string)(s)
	if alias == nil {
		return errors.New("nil is invalid value")
	}
	if err := (validate.Array{
		MinLength:    1,
		MinLengthSet: true,
		MaxLength:    0,
		MaxLengthSet: false,
	}).ValidateLength(len(alias)); err != nil {
		return errors.Wrap(err, "array")
	}
	if err := validate.UniqueItems(alias); err != nil {
		return errors.Wrap(err, "array")
	}
	return nil
}

func (s GetV1RequestsVerificationConfigurationOKApplicationJSON) Validate() error {
	alias := ([]VerificationConfig)(s)
	if alias == nil {
		return errors.New("nil is invalid value")
	}
	return nil
}

func (s GetV2HealthrecordsMychartSearchOKApplicationJSON) Validate() error {
	alias := ([]OrgInfo)(s)
	if alias == nil {
		return errors.New("nil is invalid value")
	}
	return nil
}

func (s GetV2HealthrecordsPatientIdRecordsRecordTypeOKApplicationJSON) Validate() error {
	alias := ([]Record)(s)
	if alias == nil {
		return errors.New("nil is invalid value")
	}
	var failures []validate.FieldError
	for i, elem := range alias {
		if err := func() error {
			if err := elem.Validate(); err != nil {
				return err
			}
			return nil
		}(); err != nil {
			failures = append(failures, validate.FieldError{
				Name:  fmt.Sprintf("[%d]", i),
				Error: err,
			})
		}
	}
	if len(failures) > 0 {
		return &validate.Error{Fields: failures}
	}
	return nil
}

func (s *GetV2HealthrecordsPatientIdUploadOK) Validate() error {
	if s == nil {
		return validate.ErrNilPointer
	}

	var failures []validate.FieldError
	if err := func() error {
		if value, ok := s.RegionID.Get(); ok {
			if err := func() error {
				if err := (validate.Float{}).Validate(float64(value)); err != nil {
					return errors.Wrap(err, "float")
				}
				return nil
			}(); err != nil {
				return err
			}
		}
		return nil
	}(); err != nil {
		failures = append(failures, validate.FieldError{
			Name:  "regionID",
			Error: err,
		})
	}
	if len(failures) > 0 {
		return &validate.Error{Fields: failures}
	}
	return nil
}

func (s GetV2OrdersOKApplicationJSON) Validate() error {
	alias := ([]Order)(s)
	if alias == nil {
		return errors.New("nil is invalid value")
	}
	var failures []validate.FieldError
	for i, elem := range alias {
		if err := func() error {
			if err := elem.Validate(); err != nil {
				return err
			}
			return nil
		}(); err != nil {
			failures = append(failures, validate.FieldError{
				Name:  fmt.Sprintf("[%d]", i),
				Error: err,
			})
		}
	}
	if len(failures) > 0 {
		return &validate.Error{Fields: failures}
	}
	return nil
}

func (s *GetV2PatientIdOK) Validate() error {
	if s == nil {
		return validate.ErrNilPointer
	}

	var failures []validate.FieldError
	if err := func() error {
		if value, ok := s.Record.Get(); ok {
			if err := func() error {
				if err := value.Validate(); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return err
			}
		}
		return nil
	}(); err != nil {
		failures = append(failures, validate.FieldError{
			Name:  "record",
			Error: err,
		})
	}
	if len(failures) > 0 {
		return &validate.Error{Fields: failures}
	}
	return nil
}

func (s GetV2ProvidersOKApplicationJSON) Validate() error {
	alias := ([]Plan)(s)
	if alias == nil {
		return errors.New("nil is invalid value")
	}
	return nil
}

func (s GetV2SecondopiniontDoctorsSearchOKApplicationJSON) Validate() error {
	alias := ([]SOCPSODoctor)(s)
	if alias == nil {
		return errors.New("nil is invalid value")
	}
	return nil
}

func (s *IncompleteRequest) Validate() error {
	if s == nil {
		return validate.ErrNilPointer
	}

	var failures []validate.FieldError
	if err := func() error {
		if err := s.RequestData.Validate(); err != nil {
			return err
		}
		return nil
	}(); err != nil {
		failures = append(failures, validate.FieldError{
			Name:  "requestData",
			Error: err,
		})
	}
	if err := func() error {
		if err := s.LastCompletedStep.Validate(); err != nil {
			return err
		}
		return nil
	}(); err != nil {
		failures = append(failures, validate.FieldError{
			Name:  "lastCompletedStep",
			Error: err,
		})
	}
	if len(failures) > 0 {
		return &validate.Error{Fields: failures}
	}
	return nil
}

func (s *IncompleteRequestMetadata) Validate() error {
	if s == nil {
		return validate.ErrNilPointer
	}

	var failures []validate.FieldError
	if err := func() error {
		if err := s.Status.Validate(); err != nil {
			return err
		}
		return nil
	}(); err != nil {
		failures = append(failures, validate.FieldError{
			Name:  "status",
			Error: err,
		})
	}
	if err := func() error {
		if err := (validate.Float{}).Validate(float64(s.ClinicId)); err != nil {
			return errors.Wrap(err, "float")
		}
		return nil
	}(); err != nil {
		failures = append(failures, validate.FieldError{
			Name:  "clinicId",
			Error: err,
		})
	}
	if len(failures) > 0 {
		return &validate.Error{Fields: failures}
	}
	return nil
}

func (s IncompleteRequestMetadataStatus) Validate() error {
	switch s {
	case "INCOMPLETE":
		return nil
	case "COMPLETE":
		return nil
	case "EXPIRED":
		return nil
	default:
		return errors.Errorf("invalid value: %v", s)
	}
}

func (s *IncompleteRequestResponse) Validate() error {
	if s == nil {
		return validate.ErrNilPointer
	}

	var failures []validate.FieldError
	if err := func() error {
		if err := s.RequestData.Validate(); err != nil {
			return err
		}
		return nil
	}(); err != nil {
		failures = append(failures, validate.FieldError{
			Name:  "requestData",
			Error: err,
		})
	}
	if err := func() error {
		if err := s.LastCompletedStep.Validate(); err != nil {
			return err
		}
		return nil
	}(); err != nil {
		failures = append(failures, validate.FieldError{
			Name:  "lastCompletedStep",
			Error: err,
		})
	}
	if len(failures) > 0 {
		return &validate.Error{Fields: failures}
	}
	return nil
}

func (s *NewRequestMultipart) Validate() error {
	if s == nil {
		return validate.ErrNilPointer
	}

	var failures []validate.FieldError
	if err := func() error {
		if value, ok := s.RequestBody.Get(); ok {
			if err := func() error {
				if err := value.Validate(); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return err
			}
		}
		return nil
	}(); err != nil {
		failures = append(failures, validate.FieldError{
			Name:  "requestBody",
			Error: err,
		})
	}
	if err := func() error {
		if value, ok := s.PaymentToken.Get(); ok {
			if err := func() error {
				if err := value.Validate(); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return err
			}
		}
		return nil
	}(); err != nil {
		failures = append(failures, validate.FieldError{
			Name:  "paymentToken",
			Error: err,
		})
	}
	if len(failures) > 0 {
		return &validate.Error{Fields: failures}
	}
	return nil
}

func (s *NewUPHRequestMultipart) Validate() error {
	if s == nil {
		return validate.ErrNilPointer
	}

	var failures []validate.FieldError
	if err := func() error {
		if value, ok := s.RequestBody.Get(); ok {
			if err := func() error {
				if err := value.Validate(); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return err
			}
		}
		return nil
	}(); err != nil {
		failures = append(failures, validate.FieldError{
			Name:  "requestBody",
			Error: err,
		})
	}
	if err := func() error {
		if value, ok := s.PaymentToken.Get(); ok {
			if err := func() error {
				if err := value.Validate(); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return err
			}
		}
		return nil
	}(); err != nil {
		failures = append(failures, validate.FieldError{
			Name:  "paymentToken",
			Error: err,
		})
	}
	if len(failures) > 0 {
		return &validate.Error{Fields: failures}
	}
	return nil
}

func (s NotificationMethod) Validate() error {
	switch s {
	case "Fax":
		return nil
	case "Email":
		return nil
	default:
		return errors.Errorf("invalid value: %v", s)
	}
}

func (s OnboardingTask) Validate() error {
	switch s {
	case 1:
		return nil
	case 2:
		return nil
	case 4:
		return nil
	case 8:
		return nil
	default:
		return errors.Errorf("invalid value: %v", s)
	}
}

func (s OneOrMore) Validate() error {
	switch s {
	case 0:
		return nil
	case 1:
		return nil
	case 2:
		return nil
	case 99:
		return nil
	default:
		return errors.Errorf("invalid value: %v", s)
	}
}

func (s *Order) Validate() error {
	if s == nil {
		return validate.ErrNilPointer
	}

	var failures []validate.FieldError
	if err := func() error {
		if value, ok := s.PaymentMethod.Get(); ok {
			if err := func() error {
				if err := value.Validate(); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return err
			}
		}
		return nil
	}(); err != nil {
		failures = append(failures, validate.FieldError{
			Name:  "payment_method",
			Error: err,
		})
	}
	if len(failures) > 0 {
		return &validate.Error{Fields: failures}
	}
	return nil
}

func (s *OrganVisualizationRequest) Validate() error {
	if s == nil {
		return validate.ErrNilPointer
	}

	var failures []validate.FieldError
	if err := func() error {
		if s.BodyParts == nil {
			return errors.New("nil is invalid value")
		}
		return nil
	}(); err != nil {
		failures = append(failures, validate.FieldError{
			Name:  "body_parts",
			Error: err,
		})
	}
	if len(failures) > 0 {
		return &validate.Error{Fields: failures}
	}
	return nil
}

func (s PatientStatus) Validate() error {
	switch s {
	case "cancelled":
		return nil
	case "cancel-initiated":
		return nil
	case "confirmed":
		return nil
	case "opted-out":
		return nil
	case "to-reschedule":
		return nil
	default:
		return errors.Errorf("invalid value: %v", s)
	}
}

func (s *PaymentCard) Validate() error {
	if s == nil {
		return validate.ErrNilPointer
	}

	var failures []validate.FieldError
	if err := func() error {
		if value, ok := s.ExpiryYear.Get(); ok {
			if err := func() error {
				if err := (validate.Float{}).Validate(float64(value)); err != nil {
					return errors.Wrap(err, "float")
				}
				return nil
			}(); err != nil {
				return err
			}
		}
		return nil
	}(); err != nil {
		failures = append(failures, validate.FieldError{
			Name:  "expiryYear",
			Error: err,
		})
	}
	if err := func() error {
		if value, ok := s.ExpiryMonth.Get(); ok {
			if err := func() error {
				if err := (validate.Float{}).Validate(float64(value)); err != nil {
					return errors.Wrap(err, "float")
				}
				return nil
			}(); err != nil {
				return err
			}
		}
		return nil
	}(); err != nil {
		failures = append(failures, validate.FieldError{
			Name:  "expiryMonth",
			Error: err,
		})
	}
	if len(failures) > 0 {
		return &validate.Error{Fields: failures}
	}
	return nil
}

func (s PaymentError) Validate() error {
	switch s {
	case "Declined":
		return nil
	case "Expired":
		return nil
	case "CardTypeNotAccepted":
		return nil
	case "IncorrectCVC":
		return nil
	case "IncorrectCardNumber":
		return nil
	case "GenericError":
		return nil
	default:
		return errors.Errorf("invalid value: %v", s)
	}
}

func (s PaymentStatus) Validate() error {
	switch s {
	case "prepaid":
		return nil
	case "purchased":
		return nil
	case "gracePeriod":
		return nil
	case "unpaid":
		return nil
	case "premium":
		return nil
	default:
		return errors.Errorf("invalid value: %v", s)
	}
}

func (s *PaymentToken) Validate() error {
	if s == nil {
		return validate.ErrNilPointer
	}

	var failures []validate.FieldError
	if err := func() error {
		if value, ok := s.PaymentProvider.Get(); ok {
			if err := func() error {
				if err := value.Validate(); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return err
			}
		}
		return nil
	}(); err != nil {
		failures = append(failures, validate.FieldError{
			Name:  "paymentProvider",
			Error: err,
		})
	}
	if len(failures) > 0 {
		return &validate.Error{Fields: failures}
	}
	return nil
}

func (s PaymentTokenPaymentProvider) Validate() error {
	switch s {
	case "Stripe":
		return nil
	default:
		return errors.Errorf("invalid value: %v", s)
	}
}

func (s *PendingRequest) Validate() error {
	if s == nil {
		return validate.ErrNilPointer
	}

	var failures []validate.FieldError
	if err := func() error {
		if value, ok := s.Contents.Get(); ok {
			if err := func() error {
				if err := value.Validate(); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return err
			}
		}
		return nil
	}(); err != nil {
		failures = append(failures, validate.FieldError{
			Name:  "contents",
			Error: err,
		})
	}
	if len(failures) > 0 {
		return &validate.Error{Fields: failures}
	}
	return nil
}

func (s *Physician) Validate() error {
	if s == nil {
		return validate.ErrNilPointer
	}

	var failures []validate.FieldError
	if err := func() error {
		if value, ok := s.DefaultNotificationMethod.Get(); ok {
			if err := func() error {
				if err := value.Validate(); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return err
			}
		}
		return nil
	}(); err != nil {
		failures = append(failures, validate.FieldError{
			Name:  "defaultNotificationMethod",
			Error: err,
		})
	}
	if len(failures) > 0 {
		return &validate.Error{Fields: failures}
	}
	return nil
}

func (s *PhysicianAccount) Validate() error {
	if s == nil {
		return validate.ErrNilPointer
	}

	var failures []validate.FieldError
	if err := func() error {
		var failures []validate.FieldError
		for i, elem := range s.Physicians {
			if err := func() error {
				if err := elem.Validate(); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				failures = append(failures, validate.FieldError{
					Name:  fmt.Sprintf("[%d]", i),
					Error: err,
				})
			}
		}
		if len(failures) > 0 {
			return &validate.Error{Fields: failures}
		}
		return nil
	}(); err != nil {
		failures = append(failures, validate.FieldError{
			Name:  "physicians",
			Error: err,
		})
	}
	if err := func() error {
		if value, ok := s.PermissionsMap.Get(); ok {
			if err := func() error {
				if err := value.Validate(); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return err
			}
		}
		return nil
	}(); err != nil {
		failures = append(failures, validate.FieldError{
			Name:  "permissionsMap",
			Error: err,
		})
	}
	if len(failures) > 0 {
		return &validate.Error{Fields: failures}
	}
	return nil
}

func (s PhysicianAccountPermissionsMap) Validate() error {
	var failures []validate.FieldError
	for key, elem := range s {
		if err := func() error {
			if elem == nil {
				return errors.New("nil is invalid value")
			}
			return nil
		}(); err != nil {
			failures = append(failures, validate.FieldError{
				Name:  key,
				Error: err,
			})
		}
	}

	if len(failures) > 0 {
		return &validate.Error{Fields: failures}
	}
	return nil
}

func (s PostPhysicianAccountsRequestOKApplicationJSON) Validate() error {
	alias := ([]PhysicianRecordResponse)(s)
	if alias == nil {
		return errors.New("nil is invalid value")
	}
	return nil
}

func (s *PostPhysicianAccountsRequestReq) Validate() error {
	if s == nil {
		return validate.ErrNilPointer
	}

	var failures []validate.FieldError
	if err := func() error {
		if s.StudyRequests == nil {
			return errors.New("nil is invalid value")
		}
		return nil
	}(); err != nil {
		failures = append(failures, validate.FieldError{
			Name:  "studyRequests",
			Error: err,
		})
	}
	if len(failures) > 0 {
		return &validate.Error{Fields: failures}
	}
	return nil
}

func (s *PostRequestsIDResubmitReq) Validate() error {
	if s == nil {
		return validate.ErrNilPointer
	}

	var failures []validate.FieldError
	if err := func() error {
		if value, ok := s.RequestBody.Get(); ok {
			if err := func() error {
				if err := value.Validate(); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return err
			}
		}
		return nil
	}(); err != nil {
		failures = append(failures, validate.FieldError{
			Name:  "requestBody",
			Error: err,
		})
	}
	if len(failures) > 0 {
		return &validate.Error{Fields: failures}
	}
	return nil
}

func (s *PostV1AppointmentsPatientstatusReq) Validate() error {
	if s == nil {
		return validate.ErrNilPointer
	}

	var failures []validate.FieldError
	if err := func() error {
		if value, ok := s.Records.Get(); ok {
			if err := func() error {
				if err := value.Validate(); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return err
			}
		}
		return nil
	}(); err != nil {
		failures = append(failures, validate.FieldError{
			Name:  "records",
			Error: err,
		})
	}
	if len(failures) > 0 {
		return &validate.Error{Fields: failures}
	}
	return nil
}

func (s PostV2SecondopinionEligiblePriorsOKApplicationJSON) Validate() error {
	alias := ([]string)(s)
	if alias == nil {
		return errors.New("nil is invalid value")
	}
	return nil
}

func (s *PostV2TransfersTransferIdReportdcmReq) Validate() error {
	if s == nil {
		return validate.ErrNilPointer
	}

	var failures []validate.FieldError
	if err := func() error {
		if value, ok := s.Metadata.Get(); ok {
			if err := func() error {
				if err := value.Validate(); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return err
			}
		}
		return nil
	}(); err != nil {
		failures = append(failures, validate.FieldError{
			Name:  "metadata",
			Error: err,
		})
	}
	if len(failures) > 0 {
		return &validate.Error{Fields: failures}
	}
	return nil
}

func (s *Provider) Validate() error {
	if s == nil {
		return validate.ErrNilPointer
	}

	var failures []validate.FieldError
	if err := func() error {
		if value, ok := s.FeeAmount.Get(); ok {
			if err := func() error {
				if err := (validate.Float{}).Validate(float64(value)); err != nil {
					return errors.Wrap(err, "float")
				}
				return nil
			}(); err != nil {
				return err
			}
		}
		return nil
	}(); err != nil {
		failures = append(failures, validate.FieldError{
			Name:  "feeAmount",
			Error: err,
		})
	}
	if len(failures) > 0 {
		return &validate.Error{Fields: failures}
	}
	return nil
}

func (s *ProviderConfig) Validate() error {
	if s == nil {
		return validate.ErrNilPointer
	}

	var failures []validate.FieldError
	if err := func() error {
		if value, ok := s.Settings.Get(); ok {
			if err := func() error {
				if err := value.Validate(); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return err
			}
		}
		return nil
	}(); err != nil {
		failures = append(failures, validate.FieldError{
			Name:  "settings",
			Error: err,
		})
	}
	if len(failures) > 0 {
		return &validate.Error{Fields: failures}
	}
	return nil
}

func (s *ProviderConfigSetting) Validate() error {
	if s == nil {
		return validate.ErrNilPointer
	}

	var failures []validate.FieldError
	if err := func() error {
		var failures []validate.FieldError
		for i, elem := range s.DisabledFeatures {
			if err := func() error {
				if err := (validate.Float{}).Validate(float64(elem)); err != nil {
					return errors.Wrap(err, "float")
				}
				return nil
			}(); err != nil {
				failures = append(failures, validate.FieldError{
					Name:  fmt.Sprintf("[%d]", i),
					Error: err,
				})
			}
		}
		if len(failures) > 0 {
			return &validate.Error{Fields: failures}
		}
		return nil
	}(); err != nil {
		failures = append(failures, validate.FieldError{
			Name:  "disabledFeatures",
			Error: err,
		})
	}
	if len(failures) > 0 {
		return &validate.Error{Fields: failures}
	}
	return nil
}

func (s ProviderConfigSettings) Validate() error {
	var failures []validate.FieldError
	for key, elem := range s {
		if err := func() error {
			if err := elem.Validate(); err != nil {
				return err
			}
			return nil
		}(); err != nil {
			failures = append(failures, validate.FieldError{
				Name:  key,
				Error: err,
			})
		}
	}

	if len(failures) > 0 {
		return &validate.Error{Fields: failures}
	}
	return nil
}

func (s *PublicReferEmailThrottle) Validate() error {
	if s == nil {
		return validate.ErrNilPointer
	}

	var failures []validate.FieldError
	if err := func() error {
		if value, ok := s.EmailThrottleNumber.Get(); ok {
			if err := func() error {
				if err := (validate.Float{}).Validate(float64(value)); err != nil {
					return errors.Wrap(err, "float")
				}
				return nil
			}(); err != nil {
				return err
			}
		}
		return nil
	}(); err != nil {
		failures = append(failures, validate.FieldError{
			Name:  "email_throttle_number",
			Error: err,
		})
	}
	if err := func() error {
		if value, ok := s.AvailableNumber.Get(); ok {
			if err := func() error {
				if err := (validate.Float{}).Validate(float64(value)); err != nil {
					return errors.Wrap(err, "float")
				}
				return nil
			}(); err != nil {
				return err
			}
		}
		return nil
	}(); err != nil {
		failures = append(failures, validate.FieldError{
			Name:  "available_number",
			Error: err,
		})
	}
	if len(failures) > 0 {
		return &validate.Error{Fields: failures}
	}
	return nil
}

func (s *PutV2HealthrecordsPatientIdRecordsRecordIdReq) Validate() error {
	if s == nil {
		return validate.ErrNilPointer
	}

	var failures []validate.FieldError
	if err := func() error {
		if value, ok := s.Records.Get(); ok {
			if err := func() error {
				if err := value.Validate(); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return err
			}
		}
		return nil
	}(); err != nil {
		failures = append(failures, validate.FieldError{
			Name:  "records",
			Error: err,
		})
	}
	if len(failures) > 0 {
		return &validate.Error{Fields: failures}
	}
	return nil
}

func (s *Record) Validate() error {
	if s == nil {
		return validate.ErrNilPointer
	}

	var failures []validate.FieldError
	if err := func() error {
		if value, ok := s.TypeCode.Get(); ok {
			if err := func() error {
				if err := value.Validate(); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return err
			}
		}
		return nil
	}(); err != nil {
		failures = append(failures, validate.FieldError{
			Name:  "typeCode",
			Error: err,
		})
	}
	if err := func() error {
		if value, ok := s.Tag.Get(); ok {
			if err := func() error {
				if err := (validate.String{
					MinLength:    0,
					MinLengthSet: false,
					MaxLength:    50,
					MaxLengthSet: true,
					Email:        false,
					Hostname:     false,
					Regex:        regexMap["^[A-Za-z0-9_]*$"],
				}).Validate(string(value)); err != nil {
					return errors.Wrap(err, "string")
				}
				return nil
			}(); err != nil {
				return err
			}
		}
		return nil
	}(); err != nil {
		failures = append(failures, validate.FieldError{
			Name:  "tag",
			Error: err,
		})
	}
	if len(failures) > 0 {
		return &validate.Error{Fields: failures}
	}
	return nil
}

func (s RecordTypeCode) Validate() error {
	switch s {
	case 1:
		return nil
	case 2:
		return nil
	case 3:
		return nil
	case 4:
		return nil
	case 5:
		return nil
	case 6:
		return nil
	case 7:
		return nil
	case 8:
		return nil
	case 9:
		return nil
	case 10:
		return nil
	default:
		return errors.Errorf("invalid value: %v", s)
	}
}

func (s *RejectVerifyDetails) Validate() error {
	if s == nil {
		return validate.ErrNilPointer
	}

	var failures []validate.FieldError
	if err := func() error {
		if value, ok := s.ProviderId.Get(); ok {
			if err := func() error {
				if err := (validate.Float{}).Validate(float64(value)); err != nil {
					return errors.Wrap(err, "float")
				}
				return nil
			}(); err != nil {
				return err
			}
		}
		return nil
	}(); err != nil {
		failures = append(failures, validate.FieldError{
			Name:  "providerId",
			Error: err,
		})
	}
	if len(failures) > 0 {
		return &validate.Error{Fields: failures}
	}
	return nil
}

func (s *Request) Validate() error {
	if s == nil {
		return validate.ErrNilPointer
	}

	var failures []validate.FieldError
	if err := func() error {
		if value, ok := s.Contents.Get(); ok {
			if err := func() error {
				if err := value.Validate(); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return err
			}
		}
		return nil
	}(); err != nil {
		failures = append(failures, validate.FieldError{
			Name:  "contents",
			Error: err,
		})
	}
	if len(failures) > 0 {
		return &validate.Error{Fields: failures}
	}
	return nil
}

func (s *RequestFormConfig) Validate() error {
	if s == nil {
		return validate.ErrNilPointer
	}

	var failures []validate.FieldError
	if err := func() error {
		if value, ok := s.Provider.Get(); ok {
			if err := func() error {
				if err := value.Validate(); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return err
			}
		}
		return nil
	}(); err != nil {
		failures = append(failures, validate.FieldError{
			Name:  "provider",
			Error: err,
		})
	}
	if len(failures) > 0 {
		return &validate.Error{Fields: failures}
	}
	return nil
}

func (s RequestFormStep) Validate() error {
	switch s {
	case "PATIENT_INFO":
		return nil
	case "RECENT_EXAM_INFO":
		return nil
	case "FUTURE_EXAM_NOTIFICATIONS":
		return nil
	case "CONSENT":
		return nil
	case "PAYMENT":
		return nil
	case "COMPLETED":
		return nil
	case "PATIENT_INFO_CONFIRM":
		return nil
	default:
		return errors.Errorf("invalid value: %v", s)
	}
}

func (s *RequestStatusHistory) Validate() error {
	if s == nil {
		return validate.ErrNilPointer
	}

	var failures []validate.FieldError
	if err := func() error {
		if value, ok := s.TrackingState.Get(); ok {
			if err := func() error {
				if err := value.Validate(); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return err
			}
		}
		return nil
	}(); err != nil {
		failures = append(failures, validate.FieldError{
			Name:  "trackingState",
			Error: err,
		})
	}
	if err := func() error {
		if value, ok := s.Status.Get(); ok {
			if err := func() error {
				if err := value.Validate(); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return err
			}
		}
		return nil
	}(); err != nil {
		failures = append(failures, validate.FieldError{
			Name:  "status",
			Error: err,
		})
	}
	if len(failures) > 0 {
		return &validate.Error{Fields: failures}
	}
	return nil
}

func (s RequestStatusHistoryStatus) Validate() error {
	switch s {
	case "UNFINALIZED":
		return nil
	case "UNDER_REVIEW":
		return nil
	case "FULFILLED":
		return nil
	case "SUPPRESS":
		return nil
	case "REJECTED":
		return nil
	case "VOID":
		return nil
	case "DELEGATE_PENDING_AUTH":
		return nil
	case "VERIFICATION_PENDING":
		return nil
	case "APPROVAL_REQUIRED":
		return nil
	default:
		return errors.Errorf("invalid value: %v", s)
	}
}

func (s RequestStatusHistoryTrackingState) Validate() error {
	switch s {
	case "Action Needed":
		return nil
	case "Processing":
		return nil
	case "Transferring":
		return nil
	case "Ready":
		return nil
	default:
		return errors.Errorf("invalid value: %v", s)
	}
}

func (s *RiskEligibility) Validate() error {
	if s == nil {
		return validate.ErrNilPointer
	}

	var failures []validate.FieldError
	if err := func() error {
		if value, ok := s.BreastCancerHistory.Get(); ok {
			if err := func() error {
				if err := value.Validate(); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return err
			}
		}
		return nil
	}(); err != nil {
		failures = append(failures, validate.FieldError{
			Name:  "breast_cancer_history",
			Error: err,
		})
	}
	if err := func() error {
		if value, ok := s.HasHodgkinLymphoma.Get(); ok {
			if err := func() error {
				if err := value.Validate(); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return err
			}
		}
		return nil
	}(); err != nil {
		failures = append(failures, validate.FieldError{
			Name:  "has_hodgkin_lymphoma",
			Error: err,
		})
	}
	if err := func() error {
		if value, ok := s.HasGeneMutations.Get(); ok {
			if err := func() error {
				if err := value.Validate(); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return err
			}
		}
		return nil
	}(); err != nil {
		failures = append(failures, validate.FieldError{
			Name:  "has_gene_mutations",
			Error: err,
		})
	}
	if err := func() error {
		if value, ok := s.GeneticRiskBreastCancer.Get(); ok {
			if err := func() error {
				if err := value.Validate(); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return err
			}
		}
		return nil
	}(); err != nil {
		failures = append(failures, validate.FieldError{
			Name:  "genetic_risk_breast_cancer",
			Error: err,
		})
	}
	if err := func() error {
		if value, ok := s.HasCurrentCondition.Get(); ok {
			if err := func() error {
				if err := value.Validate(); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return err
			}
		}
		return nil
	}(); err != nil {
		failures = append(failures, validate.FieldError{
			Name:  "has_current_condition",
			Error: err,
		})
	}
	if len(failures) > 0 {
		return &validate.Error{Fields: failures}
	}
	return nil
}

func (s *Share) Validate() error {
	if s == nil {
		return validate.ErrNilPointer
	}

	var failures []validate.FieldError
	if err := func() error {
		if value, ok := s.Status.Get(); ok {
			if err := func() error {
				if err := value.Validate(); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return err
			}
		}
		return nil
	}(); err != nil {
		failures = append(failures, validate.FieldError{
			Name:  "status",
			Error: err,
		})
	}
	if err := func() error {
		if value, ok := s.Method.Get(); ok {
			if err := func() error {
				if err := value.Validate(); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return err
			}
		}
		return nil
	}(); err != nil {
		failures = append(failures, validate.FieldError{
			Name:  "method",
			Error: err,
		})
	}
	if err := func() error {
		if value, ok := s.Mode.Get(); ok {
			if err := func() error {
				if err := value.Validate(); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return err
			}
		}
		return nil
	}(); err != nil {
		failures = append(failures, validate.FieldError{
			Name:  "mode",
			Error: err,
		})
	}
	if err := func() error {
		var failures []validate.FieldError
		for i, elem := range s.HealthRecords {
			if err := func() error {
				if err := elem.Validate(); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				failures = append(failures, validate.FieldError{
					Name:  fmt.Sprintf("[%d]", i),
					Error: err,
				})
			}
		}
		if len(failures) > 0 {
			return &validate.Error{Fields: failures}
		}
		return nil
	}(); err != nil {
		failures = append(failures, validate.FieldError{
			Name:  "healthRecords",
			Error: err,
		})
	}
	if len(failures) > 0 {
		return &validate.Error{Fields: failures}
	}
	return nil
}

func (s ShareMethod) Validate() error {
	switch s {
	case "Email":
		return nil
	case "AccessPagePrint":
		return nil
	case "AccessPageFax":
		return nil
	case "ZIP":
		return nil
	case "ISO":
		return nil
	default:
		return errors.Errorf("invalid value: %v", s)
	}
}

func (s ShareMode) Validate() error {
	switch s {
	case "all":
		return nil
	case "multiple":
		return nil
	default:
		return errors.Errorf("invalid value: %v", s)
	}
}

func (s *StudyRequest) Validate() error {
	if s == nil {
		return validate.ErrNilPointer
	}

	var failures []validate.FieldError
	if err := func() error {
		if value, ok := s.Mode.Get(); ok {
			if err := func() error {
				if err := value.Validate(); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return err
			}
		}
		return nil
	}(); err != nil {
		failures = append(failures, validate.FieldError{
			Name:  "mode",
			Error: err,
		})
	}
	if len(failures) > 0 {
		return &validate.Error{Fields: failures}
	}
	return nil
}

func (s StudyRequestMode) Validate() error {
	switch s {
	case "details":
		return nil
	case "daterange":
		return nil
	default:
		return errors.Errorf("invalid value: %v", s)
	}
}

func (s *SubmitStudyNotesBody) Validate() error {
	if s == nil {
		return validate.ErrNilPointer
	}

	var failures []validate.FieldError
	if err := func() error {
		if value, ok := s.UploaderNotes.Get(); ok {
			if err := func() error {
				if err := (validate.String{
					MinLength:    0,
					MinLengthSet: false,
					MaxLength:    50000,
					MaxLengthSet: true,
					Email:        false,
					Hostname:     false,
					Regex:        nil,
				}).Validate(string(value)); err != nil {
					return errors.Wrap(err, "string")
				}
				return nil
			}(); err != nil {
				return err
			}
		}
		return nil
	}(); err != nil {
		failures = append(failures, validate.FieldError{
			Name:  "uploader_notes",
			Error: err,
		})
	}
	if len(failures) > 0 {
		return &validate.Error{Fields: failures}
	}
	return nil
}

func (s *Transfer) Validate() error {
	if s == nil {
		return validate.ErrNilPointer
	}

	var failures []validate.FieldError
	if err := func() error {
		if value, ok := s.PaymentStatus.Get(); ok {
			if err := func() error {
				if err := value.Validate(); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return err
			}
		}
		return nil
	}(); err != nil {
		failures = append(failures, validate.FieldError{
			Name:  "paymentStatus",
			Error: err,
		})
	}
	if err := func() error {
		if value, ok := s.FeeInfo.Get(); ok {
			if err := func() error {
				if err := value.Validate(); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return err
			}
		}
		return nil
	}(); err != nil {
		failures = append(failures, validate.FieldError{
			Name:  "feeInfo",
			Error: err,
		})
	}
	if len(failures) > 0 {
		return &validate.Error{Fields: failures}
	}
	return nil
}

func (s *UpdateUploadRequestBody) Validate() error {
	if s == nil {
		return validate.ErrNilPointer
	}

	var failures []validate.FieldError
	if err := func() error {
		if value, ok := s.UploaderContactMethod.Get(); ok {
			if err := func() error {
				if err := value.Validate(); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return err
			}
		}
		return nil
	}(); err != nil {
		failures = append(failures, validate.FieldError{
			Name:  "uploader_contact_method",
			Error: err,
		})
	}
	if len(failures) > 0 {
		return &validate.Error{Fields: failures}
	}
	return nil
}

func (s *UpdateUserSettingsReq) Validate() error {
	if s == nil {
		return validate.ErrNilPointer
	}

	var failures []validate.FieldError
	if err := func() error {
		if value, ok := s.Task.Get(); ok {
			if err := func() error {
				if err := value.Validate(); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return err
			}
		}
		return nil
	}(); err != nil {
		failures = append(failures, validate.FieldError{
			Name:  "task",
			Error: err,
		})
	}
	if len(failures) > 0 {
		return &validate.Error{Fields: failures}
	}
	return nil
}

func (s *UploadFileMetadata) Validate() error {
	if s == nil {
		return validate.ErrNilPointer
	}

	var failures []validate.FieldError
	if err := func() error {
		if value, ok := s.ReportType.Get(); ok {
			if err := func() error {
				if err := value.Validate(); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				return err
			}
		}
		return nil
	}(); err != nil {
		failures = append(failures, validate.FieldError{
			Name:  "reportType",
			Error: err,
		})
	}
	if len(failures) > 0 {
		return &validate.Error{Fields: failures}
	}
	return nil
}

func (s UploadReportType) Validate() error {
	switch s {
	case "MHT":
		return nil
	case "PDF":
		return nil
	default:
		return errors.Errorf("invalid value: %v", s)
	}
}

func (s UploadRequestDestinationSetBy) Validate() error {
	switch s {
	case "PROVIDER":
		return nil
	case "UPLOADER":
		return nil
	default:
		return errors.Errorf("invalid value: %v", s)
	}
}

func (s UploadRequestState) Validate() error {
	switch s {
	case "CANCELLED":
		return nil
	case "COMPLETED":
		return nil
	case "CREATED":
		return nil
	case "DECLINED":
		return nil
	case "FULFILLING":
		return nil
	case "FULFILLMENT_EXPIRED":
		return nil
	case "NOTIFICATION_FAILED":
		return nil
	case "NOTIFIED":
		return nil
	default:
		return errors.Errorf("invalid value: %v", s)
	}
}

func (s *UploadRequestStudiesResponse) Validate() error {
	if s == nil {
		return validate.ErrNilPointer
	}

	var failures []validate.FieldError
	if err := func() error {
		if s.Studies == nil {
			return errors.New("nil is invalid value")
		}
		var failures []validate.FieldError
		for i, elem := range s.Studies {
			if err := func() error {
				if err := elem.Validate(); err != nil {
					return err
				}
				return nil
			}(); err != nil {
				failures = append(failures, validate.FieldError{
					Name:  fmt.Sprintf("[%d]", i),
					Error: err,
				})
			}
		}
		if len(failures) > 0 {
			return &validate.Error{Fields: failures}
		}
		return nil
	}(); err != nil {
		failures = append(failures, validate.FieldError{
			Name:  "studies",
			Error: err,
		})
	}
	if len(failures) > 0 {
		return &validate.Error{Fields: failures}
	}
	return nil
}

func (s *UploadRequestStudy) Validate() error {
	if s == nil {
		return validate.ErrNilPointer
	}

	var failures []validate.FieldError
	if err := func() error {
		if err := s.StudyInstanceUID.Validate(); err != nil {
			return err
		}
		return nil
	}(); err != nil {
		failures = append(failures, validate.FieldError{
			Name:  "study_instance_uid",
			Error: err,
		})
	}
	if err := func() error {
		if value, ok := s.UploaderNotes.Get(); ok {
			if err := func() error {
				if err := (validate.String{
					MinLength:    0,
					MinLengthSet: false,
					MaxLength:    50000,
					MaxLengthSet: true,
					Email:        false,
					Hostname:     false,
					Regex:        nil,
				}).Validate(string(value)); err != nil {
					return errors.Wrap(err, "string")
				}
				return nil
			}(); err != nil {
				return err
			}
		}
		return nil
	}(); err != nil {
		failures = append(failures, validate.FieldError{
			Name:  "uploader_notes",
			Error: err,
		})
	}
	if len(failures) > 0 {
		return &validate.Error{Fields: failures}
	}
	return nil
}

func (s *UploadRequestStudyResponse) Validate() error {
	if s == nil {
		return validate.ErrNilPointer
	}

	var failures []validate.FieldError
	if err := func() error {
		if err := s.Study.Validate(); err != nil {
			return err
		}
		return nil
	}(); err != nil {
		failures = append(failures, validate.FieldError{
			Name:  "study",
			Error: err,
		})
	}
	if len(failures) > 0 {
		return &validate.Error{Fields: failures}
	}
	return nil
}

func (s UploadRequestType) Validate() error {
	switch s {
	case "SOLICITED":
		return nil
	case "UNSOLICITED":
		return nil
	default:
		return errors.Errorf("invalid value: %v", s)
	}
}

func (s UploadRequestUploaderContactMethod) Validate() error {
	switch s {
	case "PHONE_NUMBER":
		return nil
	case "EMAIL":
		return nil
	default:
		return errors.Errorf("invalid value: %v", s)
	}
}

func (s UploadRequestUploaderType) Validate() error {
	switch s {
	case "PROVIDER":
		return nil
	case "PATIENT":
		return nil
	default:
		return errors.Errorf("invalid value: %v", s)
	}
}

func (s V1RecordsUploadStatusGetOKApplicationJSON) Validate() error {
	alias := ([]RecordUploadStatus)(s)
	if alias == nil {
		return errors.New("nil is invalid value")
	}
	return nil
}

func (s V1UsersExamsEligibleInsightsGetOKApplicationJSON) Validate() error {
	alias := ([]ExamInsightEligibilityResponse)(s)
	if alias == nil {
		return errors.New("nil is invalid value")
	}
	return nil
}

func (s V2RequestsRidStatusHistoryPostOKApplicationJSON) Validate() error {
	alias := ([]RequestStatusHistory)(s)
	if alias == nil {
		return errors.New("nil is invalid value")
	}
	var failures []validate.FieldError
	for i, elem := range alias {
		if err := func() error {
			if err := elem.Validate(); err != nil {
				return err
			}
			return nil
		}(); err != nil {
			failures = append(failures, validate.FieldError{
				Name:  fmt.Sprintf("[%d]", i),
				Error: err,
			})
		}
	}
	if len(failures) > 0 {
		return &validate.Error{Fields: failures}
	}
	return nil
}

func (s *VerifyPhysicianNotificationMethodReq) Validate() error {
	if s == nil {
		return validate.ErrNilPointer
	}

	var failures []validate.FieldError
	if err := func() error {
		if err := s.NotificationMethod.Validate(); err != nil {
			return err
		}
		return nil
	}(); err != nil {
		failures = append(failures, validate.FieldError{
			Name:  "notification_method",
			Error: err,
		})
	}
	if len(failures) > 0 {
		return &validate.Error{Fields: failures}
	}
	return nil
}

func (s YesNoUnknown) Validate() error {
	switch s {
	case 0:
		return nil
	case 1:
		return nil
	case 99:
		return nil
	default:
		return errors.Errorf("invalid value: %v", s)
	}
}
