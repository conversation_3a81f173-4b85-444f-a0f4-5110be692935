/*
* Core API
*
* Core API for PocketHealth
*
* API version: 1.0
* Contact: <EMAIL>
* Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package coreapi

import (
	"context"
	"io"
	"mime/multipart"
	"net/http"
	"time"

	"github.com/sirupsen/logrus"
	"gitlab.com/pockethealth/coreapi/pkg/models"
	"gitlab.com/pockethealth/coreapi/pkg/services/accountservice"
	"gitlab.com/pockethealth/coreapi/pkg/services/orgs"
	"gitlab.com/pockethealth/coreapi/pkg/services/reportinsights"
	"gitlab.com/pockethealth/coreapi/pkg/services/roiservice"
	"gitlab.com/pockethealth/phutils/v10/pkg/queries"
)

// The *Router types define the required methods for binding api requests to a response for the Api of the same name
// The Router implementation should parse necessary information from the http request,
// pass the data to an ApiServicer of the same name to perform the required actions, then write the service results to the http response.
// Routers with a 'Private' prefix contain routes that require authentication, while those prefixed with 'Public' do not.
type PrivateImagesApiRouter interface {
	GetImageByID(http.ResponseWriter, *http.Request)
	GetImageMetadataByID(http.ResponseWriter, *http.Request)
	Routes() Routes
	GetPathPrefix() string
	GetMiddleware() []func(http.Handler) http.Handler
}

type PublicProvidersApiRouterV2 interface {
	GetRequestFormConfiguration(http.ResponseWriter, *http.Request)
}

type PrivateProvidersApiRouter interface {
	GetProviderConsentData(http.ResponseWriter, *http.Request)
	PostProviderConsent(http.ResponseWriter, *http.Request)
	Router
}

type PublicProvidersApiRouter interface {
	GetProviderById(http.ResponseWriter, *http.Request)
	GetProviderFormConfig(http.ResponseWriter, *http.Request)
	GetProviders(http.ResponseWriter, *http.Request)
	Routes() Routes
	GetPathPrefix() string
	GetMiddleware() []func(http.Handler) http.Handler
}

type PrivateReportsApiRouter interface {
	GetReportById(http.ResponseWriter, *http.Request)
	GetTaggedReportById(http.ResponseWriter, *http.Request)
	GetFollowUpReportById(http.ResponseWriter, *http.Request)
	GetV2FollowUpReportById(http.ResponseWriter, *http.Request)
	GetUnassociatedReports(http.ResponseWriter, *http.Request)

	GetReportExplanationByReportId(http.ResponseWriter, *http.Request)
	Routes() Routes
	GetPathPrefix() string
	GetMiddleware() []func(http.Handler) http.Handler
}

type PrivateReportMetadataApiRouter interface {
	GetReportMetadataById(http.ResponseWriter, *http.Request)
	Routes() Routes
	GetPathPrefix() string
	GetMiddleware() []func(http.Handler) http.Handler
}

type PrivateRequestsApiRouter interface {
	PutResubmitRequest(w http.ResponseWriter, r *http.Request)
	GetRequests(http.ResponseWriter, *http.Request)
	PostVerifyRejectedEmail(w http.ResponseWriter, r *http.Request)
	Routes() Routes
	GetPathPrefix() string
	GetMiddleware() []func(http.Handler) http.Handler
}

type PublicRequestsApiRouterV2 interface {
	PostIncompleteRequestsV2(http.ResponseWriter, *http.Request)
	PutIncompleteRequestsV2(http.ResponseWriter, *http.Request)
	PostIncompleteRequestVerifyV2(http.ResponseWriter, *http.Request)
	Routes() Routes
	GetPathPrefix() string
	GetMiddleware() []func(http.Handler) http.Handler
}

type PublicRequestsApiRouter interface {
	PostIncompleteRequests(http.ResponseWriter, *http.Request)
	PutIncompleteRequests(http.ResponseWriter, *http.Request)
	GetIncompleteRequestById(http.ResponseWriter, *http.Request)
	PostIncompleteRequestVerify(http.ResponseWriter, *http.Request)
	Routes() Routes
	GetPathPrefix() string
	GetMiddleware() []func(http.Handler) http.Handler
}

type DebuggerToolApiRouter interface {
	Routes() Routes
	GetPathPrefix() string
	GetMiddleware() []func(http.Handler) http.Handler
}

type PrivateSharesApiRouter interface {
	GetShares(http.ResponseWriter, *http.Request)
	PostReshare(http.ResponseWriter, *http.Request)
	PostShares(http.ResponseWriter, *http.Request)
	PutRevokeShare(http.ResponseWriter, *http.Request)
	PutExtendShare(http.ResponseWriter, *http.Request)
	PostShareDLAuth(http.ResponseWriter, *http.Request)
	Routes() Routes
	GetPathPrefix() string
	GetMiddleware() []func(http.Handler) http.Handler
}

type DLSharesApiRouter interface {
	GetSharesById(http.ResponseWriter, *http.Request)
	GetShareHealthRecordsById(http.ResponseWriter, *http.Request)
	Routes() Routes
	GetPathPrefix() string
	GetMiddleware() []func(http.Handler) http.Handler
}

type PublicSharesApiRouter interface {
	PostSharesValidate(http.ResponseWriter, *http.Request)
	Routes() Routes
	GetPathPrefix() string
	GetMiddleware() []func(http.Handler) http.Handler
}

type PrivateSubscriptionsApiRouter interface {
	PutToggleAutoRenew(w http.ResponseWriter, r *http.Request)
	Routes() Routes
	GetPathPrefix() string
	GetMiddleware() []func(http.Handler) http.Handler
}

type PrivateUsersApiRouter interface {
	GetExamThumbnailByUuid(http.ResponseWriter, *http.Request)
	GetUserExamById(http.ResponseWriter, *http.Request)
	GetUserExamsSize(http.ResponseWriter, *http.Request)
	GetUserRequestConsent(http.ResponseWriter, *http.Request)
	DeleteUsersLogout(http.ResponseWriter, *http.Request)
	PostFriendReferral(http.ResponseWriter, *http.Request)
	GetUserSettings(http.ResponseWriter, *http.Request)
	UpdateUserSettings(http.ResponseWriter, *http.Request)
	Routes() Routes
	GetPathPrefix() string
	GetMiddleware() []func(http.Handler) http.Handler
}

type PrivateUsersExamsApiRouter interface {
	GetUserExams(http.ResponseWriter, *http.Request)
	Routes() Routes
	GetPathPrefix() string
	GetMiddleware() []func(http.Handler) http.Handler
}

type PrivateShortUrlsApiRouter interface {
	Router
	PostShortUrl(http.ResponseWriter, *http.Request)
}

type PublicShortUrlsApiRouter interface {
	Router
	GetOriginalUrl(http.ResponseWriter, *http.Request)
}

type PublicUsersApiRouter interface {
	PostUsers(http.ResponseWriter, *http.Request)
	PostUsersLoginSSO(http.ResponseWriter, *http.Request)
	PostUsersLoginGoogle(http.ResponseWriter, *http.Request)
	Routes() Routes
	GetPathPrefix() string
	GetMiddleware() []func(http.Handler) http.Handler
}

type PublicPaymentApiRouter interface {
	GetPaymentProviders(http.ResponseWriter, *http.Request)
	GetDiscountCode(http.ResponseWriter, *http.Request)
	PostPaymentIntent(http.ResponseWriter, *http.Request)
	PostPaymentSuccess(http.ResponseWriter, *http.Request)
	Routes() Routes
	GetPathPrefix() string
	GetMiddleware() []func(http.Handler) http.Handler
}

type PrivatePaymentApiRouter interface {
	GetPaymentHistory(http.ResponseWriter, *http.Request)
	GetDiscountCode(http.ResponseWriter, *http.Request)
	Routes() Routes
	GetPathPrefix() string
	GetMiddleware() []func(http.Handler) http.Handler
}

type PrivateSecondOpinionApiRouter interface {
	PostSecondOpinionRequest(http.ResponseWriter, *http.Request)
	GetSecondOpinionEligibilityStatus(http.ResponseWriter, *http.Request)
	Routes() Routes
	GetPathPrefix() string
	GetMiddleware() []func(http.Handler) http.Handler
}

type PrivateRecordUploadStatusApiRouter interface {
	GetRecordUploadStatusList(w http.ResponseWriter, r *http.Request)
	Routes() Routes
	GetPathPrefix() string
	GetMiddleware() []func(http.Handler) http.Handler
}

type PrivateRhoApiRouter interface {
	PostRhoRequest(http.ResponseWriter, *http.Request)
	GetRhoEligibility(http.ResponseWriter, *http.Request)
	Routes() Routes
	GetPathPrefix() string
	GetMiddleware() []func(http.Handler) http.Handler
}

type PublicReferApiRouter interface {
	GetEmailReferThrottleByToken(http.ResponseWriter, *http.Request)
	PostEmailRefer(http.ResponseWriter, *http.Request)
	Routes() Routes
	GetPathPrefix() string
	GetMiddleware() []func(http.Handler) http.Handler
}

type PrivateInternalApiRouter interface {
	DeletePHIProfile(http.ResponseWriter, *http.Request)
	Routes() Routes
	GetPathPrefix() string
	GetMiddleware() []func(http.Handler) http.Handler
}

type PublicCheckoutApiRouter interface {
	PostActivateStudies(http.ResponseWriter, *http.Request)
	GetMetadata(http.ResponseWriter, *http.Request)
	Router
}

// ApiServicers define the api actions for each Api service
type ImagesApiServicer interface {
	GetImageByID(
		context.Context,
		string,
		string,
		string,
		string,
		bool,
	) (io.ReadCloser, int64, error)
	GetImageMetadataByID(context.Context, string, string, string) (interface{}, error)
}

type ProvidersApiServicer interface {
	GetProviderById(context.Context, int64) (interface{}, error)
	GetProviderByUrl(context.Context, string) (interface{}, error)
	GetProviderFormConfig(context.Context, int64, bool, string) (interface{}, error)
	GetProviders(context.Context, string) ([]Provider, error)
	GetProviderConsentData(context.Context, string) (interface{}, error)
	PostProviderConsent(context.Context, string, Consent, string) ([]byte, error)
	PostProviderConsentVerification(
		context.Context,
		string,
		string,
	) (ConsentEmailVerification, error)
	GetProviderConfig(context.Context) (ProviderConfig, error)
	IsAppointmentReminder(context.Context, string) (bool, error)
	VerifyProviderConsent(context.Context, string, time.Time, string) (*VerifiedConsent, error)
}

type ReportsApiServicer interface {
	GetRecordStreamingReportById(
		ctx context.Context,
		providerID int64,
		physicianAccountID string,
		objectID string,
		accept string,
	) ([]byte, error)
	GetReportById(context.Context, string, string, string, string) ([]byte, error)
	GetReportMetadataById(context.Context, string, string, string) (Report, error)
	GetTaggedReportById(context.Context, string, string) ([]byte, error)
	GetFollowUpReportById(context.Context, string, string) (interface{}, error)
	GetV2FollowUpReportById(
		context.Context,
		string,
		string,
	) ([]byte, error)
	GetQuestionsByReportId(
		context.Context,
		string,
		string,
	) ([]byte, error)
	GetUnassociatedReports(context.Context, string) (interface{}, error)
	// GetOrganVisualizationByReportId(
	// 	context.Context,
	// 	string,
	// 	string,
	// ) ([]byte, error)
	GetReportExplanationByReportId(
		context.Context,
		string,
		string,
	) ([]byte, error)
	CheckObjectAccess(
		context.Context,
		string,
		string,
		*logrus.Entry,
	) error
}

type RequestsApiServicer interface {
	GetRequests(context.Context, string) (interface{}, error)
	PostIncompleteRequests(
		context.Context,
		models.IncompleteRequest,
		string,
	) (interface{}, error)
	PostIncompleteRequestsV2(
		context.Context,
		models.IncompleteRequest,
		string,
		[]*multipart.FileHeader,
		[]*multipart.FileHeader,
		string,
	) (interface{}, error)
	PutIncompleteRequests(
		context.Context,
		string,
		models.IncompleteRequest,
		string,
	) error
	PutIncompleteRequestsV2(
		context.Context,
		string,
		models.IncompleteRequest,
		string,
		[]*multipart.FileHeader,
		[]*multipart.FileHeader,
		string,
	) error
	GetIncompleteRequestById(context.Context, string) (interface{}, error)
	PostIncompleteRequestVerify(
		context.Context,
		string,
		string,
	) (interface{}, error)
	PostIncompleteRequestVerifyV2(
		context.Context,
		string,
		string,
	) (
		response models.IncompleteRequestResponse,
		err error,
	)
	PostVerifyRejectedEmail(context.Context, string, int64, RejectVerifyDetails) error
	PutResubmitRequest(
		context.Context,
		string,
		models.Request,
		string,
		string,
		[]*multipart.FileHeader,
		[]*multipart.FileHeader,
	) ([]byte, int64, error)
	GetIdVerificationConfigs(ctx context.Context) interface{}
	PostSubmitStep(
		ctx context.Context,
		stepBody orgs.StepValidationBody,
		deviceId string,
	) (orgs.StepValidationResponse, error)
	PutSubmitStep(
		ctx context.Context,
		stepBody orgs.StepValidationBody,
		deviceId string,
	) (orgs.StepValidationResponse, error)

	AddRequest(
		ctx context.Context,
		data models.CreateRequest,
		language string,
	) (
		consentFile []byte,
		id int64,
		verificationToken string,
		idVerificationLink string,
		err error,
	)
	GetRequestStatusHistory(
		ctx context.Context,
		requestId string,
	) []roiservice.RequestStatusHistory
	PatchCancelRequest(ctx context.Context, acctId string, requestId int64) error
}

type RequestFormConfigurationApiServicer interface {
	GetFormById(
		ctx context.Context,
		formId string,
		inApp bool,
		language string,
	) (orgs.FormResponse, error)
	GetFormByProviderId(
		ctx context.Context,
		providerId int64,
		inApp bool,
		deviceId string,
		language string,
	) (orgs.FormResponse, error)
	GetFormByProviderUrl(
		ctx context.Context,
		providerUrl string,
		inApp bool,
		deviceId string,
		language string,
	) (orgs.FormResponse, error)
}

type SharesApiServicer interface {
	GetShares(context.Context, string, int, int) (interface{}, error)
	GetSharesById(
		context.Context,
		string,
		string,
		string,
		[]string,
		string,
		bool,
	) (interface{}, error)
	GetShareHealthRecordsById(context.Context, string, string, []string) ([]byte, string, error)
	PostReshare(context.Context, string, string) (interface{}, error)
	PostShares(context.Context, string, Share) (interface{}, error)
	PutRevokeShare(context.Context, string, string) error
	PostSharesValidate(
		context.Context,
		string,
		string,
		string,
		string,
		string,
		string,
	) (interface{}, error)
	PostAttachShare(ctx context.Context, acctId string, shareCredentials ShareCredentials) error
	PutExtendShare(context.Context, string) error
	PostShareDLAuth(context.Context, string, string) (interface{}, error)
	PostRecordStreamingDLAuth(context.Context, int64, string, string) (any, error)
	DownloadRecordStreamingStudy(
		ctx context.Context,
		providerID int64,
		physicianAccountID string,
		studyUID string,
		format string,
		ip string,
		includeViewer bool,
	) (any, error)
}

type SubscriptionsApiServicer interface {
	PutToggleAutoRenew(context.Context, string, AutoRenewRequest) error
}

type ShortUrlsServicer interface {
	PostShortUrl(context.Context, string) (string, error)
	GetOriginalUrl(context.Context, string) (string, error)
}

type UsersApiServicer interface {
	GetExamThumbnailByUuid(context.Context, string, string, string) (io.ReadCloser, int64, error)
	GetUserExamById(context.Context, string, string) (interface{}, error)
	GetUserExams(context.Context, string, bool, queries.Queries) ([]Exam, error)
	GetUserExamsSize(context.Context, string) (interface{}, error)
	GetUserRequestConsent(context.Context, string, int64, uint, string) ([]byte, error)
	GetNotificationsByAccount(context.Context, string, bool) (UserNotifications, error)
	DeleteUsersLogout(context.Context, string) (interface{}, error)
	PostUsers(context.Context, RegisterData, string, string, string) (string, error)
	PostUsersLoginSSO(
		context.Context,
		string,
		string,
	) (accountservice.AccountSSOLoginResponse, error)
	PostUsersLoginGoogle(
		context.Context,
		string,
		string,
	) (accountservice.GoogleSSOLoginResponse, error)
	UpdateNotificationAsReadById(ctx context.Context, accountId, notificationId string) error
	CreateUserNotification(
		ctx context.Context,
		accountId string,
		notification NotificationType,
	) (Notification, error)
	DeleteUserNotificationById(ctx context.Context, accountId, notificationId string) error
	PostFriendReferral(context.Context, string, string) error
	GetUserSettings(ctx context.Context, acctId string) (interface{}, error)
	UpdateUserSettings(
		ctx context.Context,
		acctId string,
		settings accountservice.UserSettingsRequest,
	) error
	PostLoginViaSSO(
		ctx context.Context,
		token, accountId, ip string,
	) (accountservice.AccountSSOLoginResponse, error)
	TriggerOrganVisualizationInference(context.Context, string, queries.Queries) error
	GetExamInsightsEligibility(
		context.Context,
		string,
		string,
	) (map[string]*ExamInsightsEligibility, error)
	GetReportInsights(
		context.Context,
		string,
		string,
	) (reportinsights.InsightsResponse, error)
	GetOrganVisualizationByExamId(
		context.Context,
		string,
		string,
	) ([]byte, error)
}

type PaymentApiServicer interface {
	GetPaymentProviders(ctx context.Context, country string) ([]string, error)
	GetPaymentHistory(ctx context.Context, accountId string) ([]models.TransactionHistory, error)
	GetPaymentReceiptPdf(
		ctx context.Context,
		transactionHistory *models.TransactionHistory,
		accountId string,
		language string,
	) ([]byte, error)
	GetDiscountCode(ctx context.Context, discountName string) (DiscountDetails, error)
	PostPaymentIntent(
		ctx context.Context,
		paymentIntentInfo *PaymentIntentRequest,
	) (accountservice.CreateOrderIntentResponse, error)
	PostPaymentSuccess(
		ctx context.Context,
		paymentSuccessRequest *PaymentSuccessRequest,
	) error
}

type ReferApiServicer interface {
	GetEmailReferThrottleByToken(context.Context, string) (interface{}, error)
	PostEmailRefer(context.Context, string, PublicReferral) error
}

type InternalApiServicer interface {
	DeletePHIProfile(ctx context.Context, patientId string) error
}

type SecondOpinionApiServicer interface {
	PostSecondOpinionRequest(context.Context, string, string, SORequest) (string, error)
	GetSecondOpinionEligibilityStatus(context.Context, string, string) (interface{}, error)
	CheckSecondOpinionEligiblePriors(
		context.Context,
		SoEligiblePriorsRequest,
		string,
	) ([]string, error)
	SearchSecondOpinionDoctors(context.Context, map[string]string) ([]SOCPSODoctor, error)
	CreateNewPatientEligibilityProgramsForAccount(context.Context, string, ProgramName) (int, error)
	UpdateUncompletedEligibilityProgramsForPatient(
		context.Context,
		string,
		string,
		ProgramName,
		bool,
	) error
	CreateNewPatientEligibilityProgramForPatient(
		context.Context,
		string,
		string,
		ProgramName,
		bool,
		bool,
	) error
}

type RecordUploadStatusApiServicer interface {
	GetRecordUploadStatusListForAccountID(
		context.Context,
		string,
		models.AccountType,
	) ([]models.RecordUploadStatus, error)
}

type RhoApiServicer interface {
	PostRhoRequest(context.Context, string, string) (string, error)
	GetRhoEligibility(context.Context, string, string) (interface{}, error)
}

type AppointmentRemindersApiServicer interface {
	GetAppointments(context.Context, int64, string) (AppointmentDetails, error)
	PostPatientStatus(context.Context, AppointmentReminderPatientStatusUpdate, int64) error
}

type CheckoutApiServicer interface {
	GetMetadata(context.Context, int64) (*models.CheckoutMetadata, error)
	PostActivateStudies(
		context.Context,
		string,
		ActivationKey,
		string,
		string,
	) (ActivateStudyResponse, error)
}

type RateLimiter func(next http.Handler) http.Handler
