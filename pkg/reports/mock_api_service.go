package reports

import (
	"context"

	"github.com/sirupsen/logrus"
	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
)

// MockImagesApiService is a service that implents the logic for the UsersApiServicer for testing
type MockReportsApiService struct {
	MockGetReportByID func(string, string, string, string) ([]byte, error)
}

// NewMockReportsApiService creates a default api service
func NewMockReportsApiService(
	mockReportById func(string, string, string, string) ([]byte, error),
) coreapi.ReportsApiServicer {
	return &MockReportsApiService{MockGetReportByID: mockReportById}
}

// GetReportById - Get report
func (s *MockReportsApiService) GetReportById(
	ctx context.Context,
	reportId string,
	accept string,
	acctId string,
	shareId string,
) ([]byte, error) {
	return s.MockGetReportByID(reportId, accept, acctId, shareId)
}

func (s *MockReportsApiService) GetReportMetadataById(
	ctx context.Context,
	reportId string,
	acctId string,
	shareId string,
) (coreapi.Report, error) {
	return coreapi.Report{}, nil
}

func (s *MockReportsApiService) GetUnassociatedReports(
	ctx context.Context,
	acctId string,
) (interface{}, error) {
	return nil, nil
}

func (s *MockReportsApiService) GetTaggedReportById(
	ctx context.Context,
	reportId string,
	acctId string,
) ([]byte, error) {
	return s.MockGetReportByID(reportId, "text/html", acctId, "")
}

func (s *MockReportsApiService) GetFollowUpReportById(
	ctx context.Context,
	reportId string,
	acctId string,
) (interface{}, error) {
	return s.MockGetReportByID(reportId, "text/html", acctId, "")
}

func (s *MockReportsApiService) GetV2FollowUpReportById(
	ctx context.Context,
	reportId string,
	acctId string,
) ([]byte, error) {
	return []byte{}, nil
}

func (s *MockReportsApiService) GetQuestionsByReportId(ctx context.Context, reportId string, acctId string) ([]byte, error) {
	return []byte{}, nil
}

func (s *MockReportsApiService) GetOrganVisualizationByReportId(ctx context.Context, reportId string, acctId string) ([]byte, error) {
	if reportId == "no_valid_object" {
		return nil, nil
	}
	return []byte{}, nil
}

func (s *MockReportsApiService) GetReportExplanationByReportId(ctx context.Context, reportId string, acctId string) ([]byte, error) {
	return []byte{}, nil
}

func (s *MockReportsApiService) GetRecordStreamingReportById(
	ctx context.Context,
	providerID int64,
	physicianAccountID string,
	objectID string,
	accept string,
) ([]byte, error) {
	return []byte{}, nil
}

func (s *MockReportsApiService) CheckObjectAccess(
	ctx context.Context,
	objectId string,
	acctId string,
	lg *logrus.Entry,
) error {
	return nil
}
