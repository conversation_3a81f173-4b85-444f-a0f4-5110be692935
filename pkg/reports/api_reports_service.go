/*
 * Core API
 *
 * Core API for PocketHealth
 *
 * API version: 1.0
 * Contact: <EMAIL>
 * Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package reports

import (
	"context"
	"database/sql"
	"errors"
	"fmt"

	"github.com/sirupsen/logrus"
	generatedrecordservice "gitlab.com/pockethealth/coreapi/generated/services/recordservice"
	"gitlab.com/pockethealth/coreapi/pkg/audit"
	"gitlab.com/pockethealth/coreapi/pkg/audit/models"
	"gitlab.com/pockethealth/coreapi/pkg/azureUtils"
	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
	"gitlab.com/pockethealth/coreapi/pkg/errormsgs"
	"gitlab.com/pockethealth/coreapi/pkg/exams"
	sqlExams "gitlab.com/pockethealth/coreapi/pkg/mysql/exams"
	objects "gitlab.com/pockethealth/coreapi/pkg/mysql/objects"
	sqlObjects "gitlab.com/pockethealth/coreapi/pkg/mysql/objects"
	insightsUtils "gitlab.com/pockethealth/coreapi/pkg/reports/insights"
	"gitlab.com/pockethealth/coreapi/pkg/reports/organviz"
	"gitlab.com/pockethealth/coreapi/pkg/services/orgs"
	"gitlab.com/pockethealth/coreapi/pkg/services/recordservice"
	"gitlab.com/pockethealth/coreapi/pkg/services/reportprocessor"
	bgctx "gitlab.com/pockethealth/coreapi/pkg/util/bgCtx"
	"gitlab.com/pockethealth/phutils/v10/pkg/dcmtools"
	"gitlab.com/pockethealth/phutils/v10/pkg/logutils"
)

// ReportsApiService is a service that implents the logic for the ReportsApiServicer
// This service should implement the business logic for every endpoint for the ReportsApi API.
// Include any external packages or services that will be required by this service.
type ReportsApiService struct {
	sqldb                     *sql.DB
	containerClient           *azureUtils.ContainerClient
	rpClient                  reportprocessor.RPClient
	orgSvc                    orgs.OrgService
	organvizSvc               organviz.Service
	auditService              audit.Service
	recordService             recordservice.RecordServiceClientInterface
	excludeSoftDeletedReports bool
	examService               exams.ExamServiceInterface
}

// NewReportsApiService creates a default api service
func NewReportsApiService(
	mysqlDB *sql.DB,
	containerCli *azureUtils.ContainerClient,
	rp reportprocessor.RPClient,
	orgSvc orgs.OrgService,
	organvizSvc organviz.Service,
	auditService audit.Service,
	recordService recordservice.RecordServiceClientInterface,
	excludeSoftDeletedReports bool,
	examService exams.ExamServiceInterface,
) coreapi.ReportsApiServicer {
	return &ReportsApiService{
		sqldb:                     mysqlDB,
		containerClient:           containerCli,
		rpClient:                  rp,
		orgSvc:                    orgSvc,
		organvizSvc:               organvizSvc,
		auditService:              auditService,
		recordService:             recordService,
		excludeSoftDeletedReports: excludeSoftDeletedReports,
		examService:               examService,
	}
}

// GetReportById - Get report
func (s *ReportsApiService) GetRecordStreamingReportById(
	ctx context.Context,
	providerID int64,
	physicianAccountID string,
	objectID string,
	accept string,
) ([]byte, error) {
	lg := logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
		"providerID": providerID,
		"acctId":     physicianAccountID,
	})

	uuid, err := objects.GetExamUuid(ctx, s.sqldb, objectID)
	if err != nil {
		lg.WithError(err).Error("could not get study uuid for object")
		return nil, err
	}
	lg = lg.WithField("uuid", uuid)

	// check study for report
	response, err := s.recordService.GetV1PhysicianStudies(
		ctx,
		generatedrecordservice.GetV1PhysicianStudiesParams{
			AccountID: physicianAccountID,
			UUID:      []string{uuid},
		})
	if err != nil {
		lg.WithError(err).Error("could not verify access")
		return nil, err
	}
	studies, ok := response.(*generatedrecordservice.PhysicianPatientStudies)
	if !ok {
		lg.WithError(err).Error("could not parse recordservice response")
		return nil, errors.New("failed to convert recordservice response")
	}
	// find study with matching uuid from list of studies
	var study generatedrecordservice.PhysicianPatientStudy
	for _, responseStudy := range *studies {
		if responseStudy.UUID.Value == uuid {
			study = responseStudy
		}
	}
	if study.UUID.Value == "" || !study.HasReport.Value {
		lg.WithError(err).Error("physician is not verified to view report")
		return nil, errors.New(errormsgs.ERR_NOT_AUTHORIZED)
	}

	bytes, err := s.rpClient.GetReport(ctx, objectID, accept)
	if err != nil {
		lg.WithError(err).Error("error converting report")
		return nil, err
	}

	firstName, lastName := dcmtools.ParseFirstLastName(
		ctx,
		study.DicomPatientTags.Value.PatientName.Value,
	)

	// write audit log entry for report view event
	err = s.auditService.CreatePhysicianReportViewEvent(
		ctx,
		models.EventDataPhysicianReportView{
			EventDataPhysicianStudyBase: models.EventDataPhysicianStudyBase{
				EventDataPhysicianBase: models.EventDataPhysicianBase{
					ProviderID: providerID,
					UserID:     physicianAccountID,
					UserType:   models.UserTypePhysician,
				},
				EventDataPatientBase: models.EventDataPatientBase{
					PatientFirstName: firstName,
					PatientLastName:  lastName,
					PatientBirthDate: study.DicomPatientTags.Value.PatientBirthDate.Value,
				},
				StudyUID: study.DicomStudyTags.Value.StudyInstanceUID.Value,
			},
		},
	)
	if err != nil {
		lg.WithError(err).Error("error logging physician report access")
		return nil, err
	}

	bgCtx := bgctx.GetBGCtxWithCorrelation(ctx)
	go logReportAccess(bgCtx, s.sqldb, physicianAccountID, accept, objectID, s.orgSvc)

	return bytes, nil
}

// GetReportById - Get report
func (s *ReportsApiService) GetReportById(
	ctx context.Context,
	reportId string,
	accept string,
	acctId string,
	shareId string,
) ([]byte, error) {
	lg := logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
		"reportId":     reportId,
		"acctId":       acctId,
		"shareId":      shareId,
		"acceptFormat": accept,
	})

	valid, err := sqlObjects.CanAccessObject(ctx, s.sqldb, reportId, acctId, shareId)
	if err != nil {
		lg.WithError(err).Error("error validating whether user can access report")
		return nil, err
	}
	if !valid {
		lg.Error("account/share does not have access to the report")
		return nil, errors.New(errormsgs.ERR_NOT_FOUND)
	}

	if !sqlObjects.IsActivated(ctx, s.sqldb, reportId) {
		return nil, fmt.Errorf("%s: %s", errormsgs.ERR_NEEDS_PURCHASE, reportId)
	}

	bytes, err := s.rpClient.GetReport(ctx, reportId, accept)
	if err != nil {
		lg.WithError(err).Error("error converting report")
		return nil, err
	}
	bgCtx := bgctx.GetBGCtxWithCorrelation(ctx)
	go logReportAccess(bgCtx, s.sqldb, acctId, accept, reportId, s.orgSvc)

	return bytes, nil
}

// GetReportMetadataById - Get report metadata from mysql
func (s *ReportsApiService) GetReportMetadataById(
	ctx context.Context,
	reportId string,
	acctId string,
	shareId string,
) (coreapi.Report, error) {
	lg := logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
		"reportId": reportId,
		"acctId":   acctId,
		"shareId":  shareId,
	})

	valid, err := sqlObjects.CanAccessObject(ctx, s.sqldb, reportId, acctId, shareId)
	if err != nil {
		return coreapi.Report{}, err
	}
	if !valid {
		lg.Error("account/share does not have access to the report")
		return coreapi.Report{}, errors.New(errormsgs.ERR_NOT_FOUND)
	}

	var reportMetadata coreapi.Report

	reportMetadata, err = sqlObjects.GetReportMetadata(ctx, s.sqldb, reportId, s.orgSvc)
	if err != nil {
		return coreapi.Report{}, err
	}

	return reportMetadata, err
}

// GetReportById - Get report
func (s *ReportsApiService) GetTaggedReportById(
	ctx context.Context,
	reportId string,
	acctId string,
) ([]byte, error) {
	lg := logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
		"reportId": reportId,
		"acctId":   acctId,
		"tagged":   true,
	})

	valid, err := sqlObjects.CanAccessObject(ctx, s.sqldb, reportId, acctId, "")
	if err != nil {
		lg.WithError(err).Error("error validating whether user can access report")
		return nil, err
	}
	if !valid {
		lg.Error("account does not have access to the report")
		return nil, errors.New(errormsgs.ERR_NOT_FOUND)
	}

	if !sqlObjects.IsActivated(ctx, s.sqldb, reportId) {
		return nil, fmt.Errorf("%s: %s", errormsgs.ERR_NEEDS_PURCHASE, reportId)
	}

	subdictionary := ""
	// lookup examid
	examId, err := sqlObjects.GetExamUuid(ctx, s.sqldb, reportId)
	if err != nil {
		examId = ""
	}

	if examId != "" {
		modality, bodyPart, studyDescription, err := s.examService.GetExamInfoForSubdictionary(
			ctx,
			acctId,
			examId,
		)
		if err != nil {
			lg.WithError(err).Error("could not get subdictionary info, defaulting to empty string")
		}
		subdictionary = sqlExams.IsBreastSubdictionary(modality, bodyPart, studyDescription)
	}

	bytes, err := s.rpClient.GetTaggedReport(ctx, reportId, subdictionary)
	if err != nil {
		lg.WithError(err).Error("error converting report")
		return nil, err
	}
	bgCtx := bgctx.GetBGCtxWithCorrelation(ctx)
	go logReportAccess(bgCtx, s.sqldb, acctId, "text/html-tagged", reportId, s.orgSvc)

	return bytes, nil
}

func (s *ReportsApiService) GetUnassociatedReports(
	ctx context.Context,
	acctId string,
) (interface{}, error) {
	reports, err := sqlExams.GetUnassociatedReports(
		ctx,
		s.sqldb,
		acctId,
		true,
		s.excludeSoftDeletedReports,
		s.orgSvc,
	)
	if err != nil {
		logutils.DebugCtxLogger(ctx).
			WithField("acct_id", acctId).
			WithError(err).
			Error("could not retrieve unassociated reports")
		return nil, err
	}
	return reports, nil
}

// GetReportById - Get report
func (s *ReportsApiService) GetFollowUpReportById(
	ctx context.Context,
	reportId string,
	acctId string,
) (interface{}, error) {
	lg := logutils.DebugCtxLogger(ctx).WithFields(logrus.Fields{
		"reportId": reportId,
		"acctId":   acctId,
		"tagged":   true,
	})

	valid, err := sqlObjects.CanAccessObject(ctx, s.sqldb, reportId, acctId, "")
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, errors.New(errormsgs.ERR_NOT_FOUND)
		}
		lg.WithError(err).Error("error validating whether user can access report")
		return nil, err
	}
	if !valid {
		lg.Error("account/share does not have access to the report")
		return nil, errors.New(errormsgs.ERR_NOT_FOUND)
	}

	if !sqlObjects.IsActivated(ctx, s.sqldb, reportId) {
		return nil, fmt.Errorf("%s: %s", errormsgs.ERR_NEEDS_PURCHASE, reportId)
	}

	reportContents, err := insightsUtils.DownloadPdfAndExtractText(ctx, s.containerClient, reportId)
	if err != nil {
		lg.WithError(err).Error("failed to download pdf and extract text")
		return coreapi.FollowUpReport{FollowUpExists: false}, nil
	}

	isFollowUp := insightsUtils.DetectFollow(reportContents)

	return isFollowUp, nil
}

// GetV2FollowUpReportById - Get V2 Follow up report
func (s *ReportsApiService) GetV2FollowUpReportById(
	ctx context.Context,
	reportId string,
	acctId string,
) ([]byte, error) {
	lg := logutils.CtxLogger(ctx).WithFields(logrus.Fields{
		"report_id":  reportId,
		"account_id": acctId,
	})

	err := s.CheckObjectAccess(ctx, reportId, acctId, lg)
	if err != nil {
		return nil, err
	}

	return s.rpClient.GetFollowup(ctx, reportId)
}

func (s *ReportsApiService) GetQuestionsByReportId(
	ctx context.Context,
	reportId string,
	acctId string,
) ([]byte, error) {
	lg := logutils.CtxLogger(ctx).WithFields(logrus.Fields{
		"report_id":  reportId,
		"account_id": acctId,
	})

	err := s.CheckObjectAccess(ctx, reportId, acctId, lg)
	if err != nil {
		return nil, err
	}

	return s.rpClient.GetQuestions(ctx, reportId)
}

// func (s *ReportsApiService) GetOrganVisualizationByReportId(
// 	ctx context.Context,
// 	reportId string,
// 	acctId string,
// ) ([]byte, error) {
// 	lg := logutils.CtxLogger(ctx).WithFields(logrus.Fields{
// 		"report_id":  reportId,
// 		"account_id": acctId,
// 	})

// 	// find exam from report id
// 	examUuid, err := sqlObjects.GetExamUuid(ctx, s.sqldb, reportId)
// 	if err == sql.ErrNoRows {
// 		return nil, errors.New(errormsgs.ERR_NOT_FOUND)
// 	} else if err != nil {
// 		return nil, fmt.Errorf("failed getting exam uuid from report %v: %w", reportId, err)
// 	}
// 	exam, err := s.examService.GetExamBasic(ctx, acctId, examUuid)
// 	if err != nil {
// 		if _, ok := err.(sqlExams.ErrAccountIdMismatch); ok {
// 			lg.Error(err.Error())
// 			return nil, fmt.Errorf(errormsgs.ERR_NOT_FOUND)
// 		}
// 		return nil, fmt.Errorf("failed fetching data about exam %v: %w", examUuid, err)
// 	}

// 	// we currently only support CT scans of the abdomen; don't prepare anything
// 	// for any other type of reports
// 	eligibility, err := s.organvizSvc.IsExamEligible(ctx, exam)
// 	if err != nil {
// 		if err.Error() == errormsgs.ERR_NOT_FOUND {
// 			return nil, err
// 		}
// 		return nil, fmt.Errorf("failed checking if exam is eligible: %w", err)
// 	}
// 	if !slices.Contains([]organviz.ExamEligibility{organviz.ELIGIBLE_FOR_MEDSAM, organviz.ELIGIBLE_FOR_MRI, organviz.ELIGIBLE_FOR_XRAY}, eligibility) {
// 		lg.WithField("reason", eligibility).Debug("Exam is not eligible for organ visualization")
// 		return nil, nil
// 	}

// 	err = s.CheckObjectAccess(ctx, reportId, acctId, lg)
// 	if err != nil {
// 		return nil, fmt.Errorf("access denied: %w", err)
// 	}
// 	// we want to make sure the ep call does not get terminated on context cancelled
// 	bgCtx := bgctx.GetBGCtxWithCorrelation(ctx)
// 	return s.organvizSvc.FetchVisualizationsForReport(bgCtx, reportId, acctId, eligibility, examUuid)
// }

func (s *ReportsApiService) GetReportExplanationByReportId(
	ctx context.Context,
	reportId string,
	acctId string,
) ([]byte, error) {
	lg := logutils.CtxLogger(ctx).WithFields(logrus.Fields{
		"report_id":  reportId,
		"account_id": acctId,
	})

	err := s.CheckObjectAccess(ctx, reportId, acctId, lg)
	if err != nil {
		return nil, fmt.Errorf("access denied: %w", err)
	}

	return s.rpClient.GetReportExplanation(ctx, reportId)
}

// check if the user has access to the image/report
func (s *ReportsApiService) CheckObjectAccess(
	ctx context.Context,
	objectId string,
	acctId string,
	lg *logrus.Entry,
) error {
	valid, err := sqlObjects.CanAccessObject(ctx, s.sqldb, objectId, acctId, "")
	if err != nil {
		if err == sql.ErrNoRows {
			return errors.New(errormsgs.ERR_NOT_FOUND)
		}
		lg.WithError(err).Error("error validating whether user can access object")
		return err
	}
	if !valid {
		lg.Error("account does not have access to the object")
		return errors.New(errormsgs.ERR_NOT_FOUND)
	}
	if !sqlObjects.IsActivated(ctx, s.sqldb, objectId) {
		return fmt.Errorf("%s: %s", errormsgs.ERR_NEEDS_PURCHASE, objectId)
	}
	return nil
}
