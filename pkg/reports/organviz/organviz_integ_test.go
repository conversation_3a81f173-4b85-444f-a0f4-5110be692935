//go:build integration
// +build integration

// Package organviz contains integration tests for the organviz service.
//
// IMPORTANT: These tests verify the internal organviz service functionality that is used by
// the GetOrganVisualizationByExamID endpoint. While the tests use reportId parameters for
// test setup and internal method calls, this is an implementation detail.
//
// The public API (GetOrganVisualizationByExamID) works with examId and internally calls
// these methods for each report in the exam. The reportId dependency in these tests
// reflects the current internal architecture where:
// - Blob storage uses reportId for naming (organviz/input/{reportId})
// - Database relationships link reports to exams
// - External reportinsights API expects reportId
//
// The tests are still valid and necessary because they verify the core organviz
// functionality that powers the exam-based public API.

package organviz

import (
	"bytes"
	"context"
	"database/sql"
	"fmt"
	"io"
	"sync"
	"testing"
	"time"

	"github.com/Azure/azure-sdk-for-go/sdk/azcore/policy"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
	mockrecordservice "gitlab.com/pockethealth/coreapi/generated/mocks/recordservice"
	"gitlab.com/pockethealth/coreapi/pkg/azureUtils"
	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
	"gitlab.com/pockethealth/coreapi/pkg/reports/organviz/orientation"
	"gitlab.com/pockethealth/coreapi/pkg/services/accountservice"
	"gitlab.com/pockethealth/coreapi/pkg/services/orgs"
	planservice "gitlab.com/pockethealth/coreapi/pkg/services/plans"
	"gitlab.com/pockethealth/coreapi/pkg/services/providersservice"
	"gitlab.com/pockethealth/coreapi/pkg/services/recordservice"
	"gitlab.com/pockethealth/coreapi/pkg/services/reportinsights"
	"gitlab.com/pockethealth/coreapi/pkg/services/reportprocessor"
	"gitlab.com/pockethealth/coreapi/pkg/testutils"
	"gitlab.com/pockethealth/phutils/v10/pkg/azstorageauth"

	examService "gitlab.com/pockethealth/coreapi/pkg/exams"
)

func TestIsExamEligible(t *testing.T) {
	containerClient, db, mockConvertImage := createServiceDependencies(t)
	sut := NewService(
		containerClient,
		db,
		mockConvertImage,
		nil,
		reportinsights.NewReportInsightsMock(),
	)

	orgServiceMock := &orgs.OrgServiceMock{}
	planServiceMock := &planservice.PlanSvcMock{}
	accountServiceMock := &accountservice.AcctSvcMock{}
	recordServiceMock := mockrecordservice.NewMockRecordServiceClientInterface(t)
	providersServiceMock := providersservice.NewProviderServiceMock()
	examServiceMock := examService.NewExamService(
		db,
		orgServiceMock,
		recordServiceMock,
		planServiceMock,
		accountServiceMock,
		providersServiceMock,
		&examService.MockMigrationHelper{},
	)

	reportId := "organviz-is-exam-eligible-test-report-id"
	examUuid := "organviz-is-exam-eligible-test-exam-uuid"
	acctId := "organviz-integ-test-account"

	medsamEligibleBodyPart := "abd"
	medsamEligibleModality := "ct"
	eligibleDates := []string{"********", "********"}

	xrayEligibleBodyPart := "chest"
	xrayEligibleModality := "CR"

	testCases := map[string]struct {
		exam                coreapi.ExamRawBasic
		expectedEligibility ExamEligibility
		expectError         bool
	}{
		"ct abd": {
			exam: coreapi.ExamRawBasic{
				BodyPart: medsamEligibleBodyPart,
				Modality: medsamEligibleModality,
			},
			expectedEligibility: ELIGIBLE_FOR_MEDSAM,
		},
		"ct abd - case insensitive": {
			exam: coreapi.ExamRawBasic{
				BodyPart: "AbD",
				Modality: "cT",
			},
			expectedEligibility: ELIGIBLE_FOR_MEDSAM,
		},
		"ct abdomen": {
			exam: coreapi.ExamRawBasic{
				BodyPart: "abdomen",
				Modality: "ct",
			},
			expectedEligibility: ELIGIBLE_FOR_MEDSAM,
		},
		"ct chest": {
			exam: coreapi.ExamRawBasic{
				BodyPart: "chest",
				Modality: "ct",
			},
			expectedEligibility: ELIGIBLE_FOR_MEDSAM,
		},
		"ct head chest abdomen": {
			exam: coreapi.ExamRawBasic{
				BodyPart: "head chest abdomen",
				Modality: "ct",
			},
			expectedEligibility: ELIGIBLE_FOR_MEDSAM,
		},
		"cat abd": {
			exam: coreapi.ExamRawBasic{
				BodyPart: "abd",
				Modality: "ct scan",
			},
			expectedEligibility: ELIGIBLE_FOR_MEDSAM,
		},
		"cat thorax": {
			exam: coreapi.ExamRawBasic{
				BodyPart: "thorax",
				Modality: "ct scan",
			},
			expectedEligibility: ELIGIBLE_FOR_MEDSAM,
		},
		"ct foot": {
			exam: coreapi.ExamRawBasic{
				BodyPart: "foot",
				Modality: "ct",
			},
			expectedEligibility: INELIGIBLE_BODY_PART,
		},

		"unknown body part, data in description": {
			exam: coreapi.ExamRawBasic{
				BodyPart:    "unknown",
				Modality:    "ct",
				Description: "foo abdomen bar",
			},
			expectedEligibility: ELIGIBLE_FOR_MEDSAM,
		},
		"empty body part, data in description": {
			exam: coreapi.ExamRawBasic{
				BodyPart:    "",
				Modality:    "ct",
				Description: "foo abdomen bar",
			},
			expectedEligibility: ELIGIBLE_FOR_MEDSAM,
		},
		"empty body part, no data in description": {
			exam: coreapi.ExamRawBasic{
				BodyPart:    "",
				Modality:    "ct",
				Description: "",
			},
			expectedEligibility: INELIGIBLE_BODY_PART,
		},

		"ultrasound abd": {
			exam: coreapi.ExamRawBasic{
				BodyPart: "abd",
				Modality: "ultrasound",
			},
			expectedEligibility: INELIGIBLE_MODALITY,
		},

		"patient too young": {
			exam: coreapi.ExamRawBasic{
				BodyPart:       medsamEligibleBodyPart,
				Modality:       medsamEligibleModality,
				DICOMExamDate:  "19710102",
				DICOMBirthDate: "19700101", // 366 days old
			},
			expectedEligibility: INELIGIBLE_AGE,
		},
		"patient old enough": {
			exam: coreapi.ExamRawBasic{
				BodyPart:       medsamEligibleBodyPart,
				Modality:       medsamEligibleModality,
				DICOMExamDate:  "20000101", // 30 years old
				DICOMBirthDate: "19700101",
			},
			expectedEligibility: ELIGIBLE_FOR_MEDSAM,
		},
		"invalid exam date": {
			exam: coreapi.ExamRawBasic{
				BodyPart:       medsamEligibleBodyPart,
				Modality:       medsamEligibleModality,
				DICOMExamDate:  "invalid-exam",
				DICOMBirthDate: "19691123",
			},
			expectedEligibility: UNKNOWN,
			expectError:         true,
		},
		"invalid birth date": {
			exam: coreapi.ExamRawBasic{
				BodyPart:       medsamEligibleBodyPart,
				Modality:       medsamEligibleModality,
				DICOMExamDate:  "19691123",
				DICOMBirthDate: "invalid-birth",
			},
			expectedEligibility: UNKNOWN,
			expectError:         true,
		},
		"xray cr chest": {
			exam: coreapi.ExamRawBasic{
				BodyPart: xrayEligibleBodyPart,
				Modality: xrayEligibleModality,
			},
			expectedEligibility: ELIGIBLE_FOR_XRAY,
		},
		"xray dx thorax": {
			exam: coreapi.ExamRawBasic{
				BodyPart: "thorax",
				Modality: "dx",
			},
			expectedEligibility: ELIGIBLE_FOR_XRAY,
		},
		"xray xa thorax": {
			exam: coreapi.ExamRawBasic{
				BodyPart: xrayEligibleBodyPart,
				Modality: "xa",
			},
			expectedEligibility: INELIGIBLE_MODALITY,
		},
		"xray modality in description thorax": {
			exam: coreapi.ExamRawBasic{
				BodyPart:    "thorax",
				Description: "XRAY",
			},
			expectedEligibility: ELIGIBLE_FOR_XRAY,
		},
		"xray leg": {
			exam: coreapi.ExamRawBasic{
				BodyPart: "leg",
				Modality: xrayEligibleModality,
			},
			expectedEligibility: INELIGIBLE_BODY_PART,
		},
		"totally off": {
			exam: coreapi.ExamRawBasic{
				BodyPart:    "unknown",
				Description: "scrotum",
			},
			expectedEligibility: INELIGIBLE_BODY_PART,
		},
		"mri abd": {
			exam: coreapi.ExamRawBasic{
				BodyPart: "Abdomen",
				Modality: "mri",
			},
			expectedEligibility: ELIGIBLE_FOR_MRI,
		},
		"mr chest": {
			exam: coreapi.ExamRawBasic{
				BodyPart: "chest",
				Modality: "MR",
			},
			expectedEligibility: ELIGIBLE_FOR_MRI,
		},
		"mr abdomen pelvis": {
			exam: coreapi.ExamRawBasic{
				BodyPart:    "ABDOMENPELVIS",
				Description: "MR ABD",
			},
			expectedEligibility: ELIGIBLE_FOR_MRI,
		},
		"mr abdomen pelvis in description": {
			exam: coreapi.ExamRawBasic{

				Description: "MR ABDOMEN PELVIS",
			},
			expectedEligibility: ELIGIBLE_FOR_MRI,
		},
		"mri fetal scan not eligible": {
			exam: coreapi.ExamRawBasic{

				Description: "MR abd fetal scan",
			},
			expectedEligibility: INELIGIBLE_BODY_PART,
		},
		"mri brain": {
			exam: coreapi.ExamRawBasic{
				BodyPart:    "BRAIN",
				Description: "MRI scan",
			},
			expectedEligibility: INELIGIBLE_BODY_PART,
		},
	}

	for name, testCase := range testCases {
		t.Run(name, func(t *testing.T) {
			if testCase.exam.DICOMBirthDate == "" {
				testCase.exam.DICOMBirthDate = eligibleDates[0]
			}
			if testCase.exam.DICOMExamDate == "" {
				testCase.exam.DICOMExamDate = eligibleDates[1]
			}

			CreateExamAndReport(
				t,
				db,
				reportId,
				examUuid,
				acctId,
			)
			// overwrite the exam created in the statement above
			_, err := db.Exec(
				`UPDATE exams
				    SET body_part=?, modality=?, description=?,
					    dob=?, date=?
				  WHERE uuid=?`,
				testCase.exam.BodyPart, testCase.exam.Modality, testCase.exam.Description,
				testCase.exam.DICOMBirthDate, testCase.exam.DICOMExamDate,
				examUuid,
			)
			require.NoError(t, err)

			study := recordservice.FormatPatientStudy(
				true, // activated
				recordservice.FULL_AVAILABILITY,
				true, // hasReport
				100,  //instanceUploadProgressPercent
			)
			study.UUID = examUuid
			study.DicomStudyTags.BodyPart = testCase.exam.BodyPart
			study.DicomStudyTags.Modality = testCase.exam.Modality
			study.DicomStudyTags.StudyDescription = testCase.exam.Description
			study.DicomStudyTags.StudyDate = testCase.exam.DICOMExamDate
			study.DicomPatientTags.PatientBirthDate = testCase.exam.DICOMBirthDate

			recordServiceMock.ExpectedCalls = nil
			var b *bool
			recordServiceMock.EXPECT().
				GetStudies(mock.Anything, acctId, false, false, b, []string{examUuid}).
				Return(
					[]recordservice.PatientStudy{study}, nil,
				)

			exam, err := examServiceMock.GetExamBasic(context.Background(), acctId, examUuid)
			require.NoError(t, err)

			eligibility, err := sut.IsExamEligible(context.Background(), exam)
			if testCase.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
			assert.Equal(t, testCase.expectedEligibility, eligibility)
		})
	}
}

func TestPatientAgeAtTimeOfExam(t *testing.T) {
	testCases := map[string]struct {
		birthDate string
		examDate  string
		age       int
	}{
		"exam date later in year than birth date": {
			birthDate: "********",
			examDate:  "********",
			age:       26,
		},
		"exam date earlier in year than birth date": {
			birthDate: "********",
			examDate:  "19891106",
			age:       25,
		},
		"exam date before birth date": {
			birthDate: "********",
			examDate:  "********",
			age:       -1,
		},
	}
	for name, testCase := range testCases {
		t.Run(name, func(t *testing.T) {
			age, err := patientAgeAtTimeOfExam(coreapi.ExamRawBasic{
				DICOMBirthDate: testCase.birthDate,
				DICOMExamDate:  testCase.examDate,
			})
			require.NoError(t, err)
			assert.Equal(t, testCase.age, age)
		})
	}
}

func TestPrepareReport(t *testing.T) {
	// This test verifies the internal prepareReport method which is used by
	// GetOrganVisualizationByExamID endpoint. The reportId is used for blob storage
	// naming and internal database relationships, but the public API works with examId.

	// Set up system-under-test
	containerClient, db, mockConvertImage := createServiceDependencies(t)
	sut := NewService(
		containerClient,
		db,
		mockConvertImage,
		nil,
		reportinsights.NewReportInsightsMock(),
	)

	// Insert test data
	acctId := "organviz-integ-test-account"
	reportId := "organviz-prepare-report-test-report-id"
	examUuid := "organviz-prepare-report-test-exam-uuid"
	seriesUid := "organviz-prepare-report-test-series-id"

	CreateExamAndReport(t, db, reportId, examUuid, acctId)

	blobPrefix := fmt.Sprintf("organviz/input/%s", reportId)

	testCases := map[string]struct {
		numImagesToAdd            int
		imageOrientation          string
		expectedSelectedObjectIds []string
		expectError               bool
	}{
		"prepare report with five images": {
			numImagesToAdd:   75,
			imageOrientation: "1/0/0/0/1/0",
			expectedSelectedObjectIds: []string{
				blobPrefix + "_" + seriesUid + "_img-6",
				blobPrefix + "_" + seriesUid + "_img-21",
				blobPrefix + "_" + seriesUid + "_img-36",
				blobPrefix + "_" + seriesUid + "_img-51",
				blobPrefix + "_" + seriesUid + "_img-66",
			},
			expectError: false,
		},
		"no image in exam to prepare": {
			numImagesToAdd:            5,
			imageOrientation:          "0/1/0/0/0/-1",
			expectedSelectedObjectIds: []string{},
			expectError:               true,
		},
	}

	for name, testCase := range testCases {
		t.Run(name, func(t *testing.T) {
			addImagesToSeries(
				t,
				db,
				examUuid,
				seriesUid,
				testCase.numImagesToAdd,
				testCase.imageOrientation,
			)
			if len(testCase.expectedSelectedObjectIds) > 0 {
				t.Cleanup(func() {
					testutils.DeleteBlobsWithPrefix(t, blobPrefix, containerClient.ReportInsight)
					testutils.DeleteBlobsWithPrefix(t, reportId, containerClient.ReportInsight)
				})
			}

			// Execute test
			err := sut.prepareReport(context.Background(), reportId, acctId, ELIGIBLE_FOR_MEDSAM)
			if testCase.expectError {
				assert.Error(t, err)
				if len(testCase.expectedSelectedObjectIds) == 0 {
					assert.Equal(t, err.Error(), "no object selected from the series")
				}

				// Check that expected images were uploaded to the reportinsights blob storage
				blobNames, err := testutils.GetBlobsWithPrefix(
					blobPrefix,
					containerClient.ReportInsight,
				)
				require.NoError(t, err)
				assert.ElementsMatch(t,
					testCase.expectedSelectedObjectIds,
					blobNames,
				)
			} else {
				require.NoError(t, err)

				// Check that expected images were uploaded to the reportinsights blob storage
				blobNames, err := testutils.GetBlobsWithPrefix(blobPrefix, containerClient.ReportInsight)
				require.NoError(t, err)
				assert.ElementsMatch(t,
					testCase.expectedSelectedObjectIds,
					blobNames,
				)

				blobNames, err = testutils.GetBlobsWithPrefix(reportId, containerClient.ReportInsight)
				require.NoError(t, err)
				assert.ElementsMatch(t,
					[]string{
						reportId + "_base",
					},
					blobNames,
				)
			}
		})
	}
}

func TestSelectSeriesForMedSAM(t *testing.T) {
	// This test verifies series selection logic used by the organviz service.
	// While it uses reportId for test setup, the logic is exam-based and used by GetOrganVisualizationByExamID.
	acctId := "organviz-integ-test-account"
	reportId := "organviz-select-series-test-report-id"
	examUuid := "organviz-select-series-test-exam-uuid"

	containerClient, db, mockConvertImage := createServiceDependencies(t)
	sut := NewService(
		containerClient,
		db,
		mockConvertImage,
		nil,
		reportinsights.NewReportInsightsMock(),
	)

	validOrientation := orientation.AXIAL
	validSopClass := ""
	validNumImages := 1
	testCases := map[string]struct {
		series                    []seriesData
		expectedSelectedSeriesUid *string
	}{
		"one valid and one invalid series based on orientation": {
			series: []seriesData{{
				orientation: validOrientation,
				sopClass:    validSopClass,
				numImages:   validNumImages,
			}, {
				orientation: orientation.SAGITTAL, // invalid orientation
				sopClass:    validSopClass,
				numImages:   validNumImages,
			}},
			// expect series-0, the first series in `series` above
			expectedSelectedSeriesUid: asPointer("organviz-integ-test-series-0"),
		},
		"one valid and one invalid series based on SOP class": {
			series: []seriesData{{
				orientation: validOrientation,
				sopClass:    secondaryCaptureImageStorage, // invalid SOP class
				numImages:   validNumImages,
			}, {
				orientation: validOrientation,
				sopClass:    validSopClass,
				numImages:   validNumImages,
			}},
			// expect second series
			expectedSelectedSeriesUid: asPointer("organviz-integ-test-series-1"),
		},
		"no valid series": {
			series: []seriesData{{
				orientation: orientation.SAGITTAL,
				sopClass:    "",
				numImages:   1,
			}},
			expectedSelectedSeriesUid: nil,
		},
		"selects series with most images": {
			series: []seriesData{{
				orientation: validOrientation,
				sopClass:    validSopClass,
				numImages:   2,
			}, {
				orientation: validOrientation,
				sopClass:    validSopClass,
				numImages:   10,
			}},
			// expect second series
			expectedSelectedSeriesUid: asPointer("organviz-integ-test-series-1"),
		},
	}

	for name, testCase := range testCases {
		t.Run(name, func(t *testing.T) {
			CreateExamAndReport(t, db, reportId, examUuid, acctId)

			for i, series := range testCase.series {
				images := addImagesToSeries(
					t,
					db,
					examUuid,
					fmt.Sprintf("organviz-integ-test-series-%d", i),
					series.numImages,
					"1/0/0/0/1/0",
				)
				addViewMetaDataToObject(t, db, images[0], map[string]any{
					"image_orientation": series.orientation.ToString(),
					"sop_class_uid":     series.sopClass,
				})
			}

			series, err := sut.selectValidSeriesForMedSAM(context.Background(), examUuid)
			require.NoError(t, err)
			if testCase.expectedSelectedSeriesUid == nil {
				assert.Nil(t, series)
			} else {
				assert.Equal(t, *testCase.expectedSelectedSeriesUid, *series)
			}
		})
	}
}

func TestSelectValidSeriesForXRAY(t *testing.T) {
	// This test verifies X-ray series selection logic used by the organviz service.
	// While it uses reportId for test setup, the logic is exam-based and used by GetOrganVisualizationByExamID.
	acctId := "organviz-integ-test-account"
	reportId := "organviz-select-series-test-report-id"
	examUuid := "organviz-select-series-test-exam-uuid"

	containerClient, db, mockConvertImage := createServiceDependencies(t)
	sut := NewService(
		containerClient,
		db,
		mockConvertImage,
		nil,
		reportinsights.NewReportInsightsMock(),
	)

	bestViewPosition := "PA"
	validSopClass := ""
	validNumImages := 1
	testCases := map[string]struct {
		series                    []seriesData
		expectedSelectedSeriesUid *string
	}{
		"one valid and one invalid series based on view position": {
			series: []seriesData{{
				viewPosition: bestViewPosition,
				sopClass:     validSopClass,
				numImages:    validNumImages,
			}, {
				viewPosition: "AP",
				sopClass:     validSopClass,
				numImages:    validNumImages,
			}},
			// expect series-0, the first series in `series` above
			expectedSelectedSeriesUid: asPointer("organviz-integ-test-series-0"),
		},
		"one valid and one invalid series based on SOP class": {
			series: []seriesData{{
				viewPosition: bestViewPosition,
				sopClass:     secondaryCaptureImageStorage, // invalid SOP class
				numImages:    validNumImages,
			}, {
				viewPosition: "LATERAL",
				sopClass:     validSopClass,
				numImages:    validNumImages,
			}},
			// expect second series
			expectedSelectedSeriesUid: asPointer("organviz-integ-test-series-1"),
		},
		"no valid series": {
			series: []seriesData{{
				viewPosition: "",
				sopClass:     secondaryCaptureImageStorage,
				numImages:    1,
			}},
			expectedSelectedSeriesUid: nil,
		},
		"select PA series in description": {
			series: []seriesData{{
				viewPosition: "",
				description:  "CHEST PA",
				sopClass:     validSopClass,
				numImages:    2,
			}, {
				viewPosition: "LAT",
				sopClass:     validSopClass,
				numImages:    10,
			},
				{
					viewPosition: "",
					description:  "CHEST AP",
					sopClass:     validSopClass,
					numImages:    2,
				}},
			// expect second series
			expectedSelectedSeriesUid: asPointer("organviz-integ-test-series-0"),
		},
		"select AP series in description": {
			series: []seriesData{{
				viewPosition: "",
				description:  "",
				sopClass:     validSopClass,
				numImages:    2,
			},
				{
					viewPosition: "LAT",
					sopClass:     validSopClass,
					numImages:    10,
				},
				{
					viewPosition: "",
					description:  "CHEST AP",
					sopClass:     validSopClass,
					numImages:    2,
				}},
			// expect second series
			expectedSelectedSeriesUid: asPointer("organviz-integ-test-series-2"),
		},
	}

	for name, testCase := range testCases {
		t.Run(name, func(t *testing.T) {
			CreateExamAndReport(t, db, reportId, examUuid, acctId)

			for i, series := range testCase.series {
				images := addImagesToSeries(
					t,
					db,
					examUuid,
					fmt.Sprintf("organviz-integ-test-series-%d", i),
					series.numImages,
					"1/0/0/0/1/0",
				)
				createSeries(
					t,
					db,
					examUuid,
					fmt.Sprintf("organviz-integ-test-series-%d", i),
					series.description,
				)
				addViewMetaDataToObject(t, db, images[0], map[string]any{
					"sop_class_uid": series.sopClass,
					"view_position": series.viewPosition,
				})
			}

			series, err := sut.selectValidSeriesForXRAY(context.Background(), examUuid)
			require.NoError(t, err)
			if testCase.expectedSelectedSeriesUid == nil {
				assert.Nil(t, series)
			} else {
				assert.Equal(t, *testCase.expectedSelectedSeriesUid, *series)
			}
		})
	}
}

func TestSelectSeriesForMRI(t *testing.T) {
	// This test verifies MRI series selection logic used by the organviz service.
	// While it uses reportId for test setup, the logic is exam-based and used by GetOrganVisualizationByExamID.
	acctId := "organviz-integ-test-account"
	reportId := "organviz-select-series-test-report-id"
	examUuid := "organviz-select-series-test-exam-uuid"

	containerClient, db, mockConvertImage := createServiceDependencies(t)
	sut := NewService(
		containerClient,
		db,
		mockConvertImage,
		nil,
		reportinsights.NewReportInsightsMock(),
	)

	validOrientation := orientation.AXIAL
	validSopClass := ""
	validNumImages := 1
	testCases := map[string]struct {
		series                    []seriesData
		expectedSelectedSeriesUid *string
	}{
		"one valid and one invalid series based on orientation": {
			series: []seriesData{{
				orientation: validOrientation,
				sopClass:    validSopClass,
				numImages:   validNumImages,
				description: "AX T1 Post",
			}, {
				orientation: orientation.SAGITTAL, // invalid orientation
				sopClass:    validSopClass,
				numImages:   validNumImages,
				description: "AX T1 pre",
			}},
			// expect series-0, the first series in `series` above
			expectedSelectedSeriesUid: asPointer("organviz-integ-test-series-0"),
		},
		"one valid and one invalid series based on SOP class": {
			series: []seriesData{{
				orientation: validOrientation,
				sopClass:    secondaryCaptureImageStorage, // invalid SOP class
				numImages:   validNumImages,
				description: "AX T1 Post",
			}, {
				orientation: validOrientation,
				sopClass:    validSopClass,
				numImages:   validNumImages,
				description: "AX T1 Pre",
			}},
			// expect second series
			expectedSelectedSeriesUid: asPointer("organviz-integ-test-series-1"),
		},
		"no valid series": {
			series: []seriesData{{
				orientation: orientation.SAGITTAL,
				sopClass:    "",
				numImages:   1,
				description: "AX eADC_DWI",
			}, {
				orientation: orientation.AXIAL,
				sopClass:    "",
				numImages:   1,
				description: "AX eADC_DWI",
			}},
			expectedSelectedSeriesUid: nil,
		},
		"selects t1 series with most images": {
			series: []seriesData{{
				orientation: validOrientation,
				sopClass:    validSopClass,
				numImages:   2,
				description: "AX T1 Post",
			}, {
				orientation: validOrientation,
				sopClass:    validSopClass,
				numImages:   10,
				description: "AX t1 pre",
			}},
			// expect second series
			expectedSelectedSeriesUid: asPointer("organviz-integ-test-series-1"),
		},
	}

	for name, testCase := range testCases {
		t.Run(name, func(t *testing.T) {
			CreateExamAndReport(t, db, reportId, examUuid, acctId)

			for i, series := range testCase.series {
				images := addImagesToSeries(
					t,
					db,
					examUuid,
					fmt.Sprintf("organviz-integ-test-series-%d", i),
					series.numImages,
					"1/0/0/0/1/0",
				)
				createSeries(
					t,
					db,
					examUuid,
					fmt.Sprintf("organviz-integ-test-series-%d", i),
					series.description,
				)
				addViewMetaDataToObject(t, db, images[0], map[string]any{
					"image_orientation": series.orientation.ToString(),
					"sop_class_uid":     series.sopClass,
				})
			}

			series, err := sut.selectValidSeriesForMRI(context.Background(), examUuid)
			require.NoError(t, err)
			if testCase.expectedSelectedSeriesUid == nil {
				assert.Nil(t, series)
			} else {
				assert.Equal(t, *testCase.expectedSelectedSeriesUid, *series)
			}
		})
	}
}

func TestSelectImages(t *testing.T) {
	// This test verifies image selection logic used by the organviz service.
	// While it uses reportId for test setup, the logic is exam-based and used by GetOrganVisualizationByExamID.
	acctId := "organviz-integ-test-account"
	reportId := "organviz-select-images-test-report-id"
	examUuid := "organviz-select-images-test-exam-uuid"
	seriesUid := "organviz-select-images-test-series"

	testCases := []struct {
		numImages            int
		expectedImagesChoses []string
		expectError          bool
	}{{
		numImages:            0,
		expectError:          false,
		expectedImagesChoses: []string{},
	}, {
		numImages:            1,
		expectedImagesChoses: []string{seriesUid + "_img-0"},
	}, {
		numImages: 5,
		expectedImagesChoses: []string{
			seriesUid + "_img-0",
			seriesUid + "_img-1",
			seriesUid + "_img-2",
			seriesUid + "_img-3",
			seriesUid + "_img-4",
		},
	}, {
		numImages: 6,
		expectedImagesChoses: []string{
			seriesUid + "_img-0",
			seriesUid + "_img-1",
			seriesUid + "_img-2",
			seriesUid + "_img-3",
			seriesUid + "_img-4",
		},
	}, {
		numImages: 8, // added after crash detected in prod
		expectedImagesChoses: []string{
			seriesUid + "_img-0",
			seriesUid + "_img-2",
			seriesUid + "_img-4",
			seriesUid + "_img-6",
		},
	}, {
		numImages: 100,
		expectedImagesChoses: []string{
			seriesUid + "_img-9",
			seriesUid + "_img-29",
			seriesUid + "_img-49",
			seriesUid + "_img-69",
			seriesUid + "_img-89",
		},
	},
	}

	containerClient, db, mockConvertImage := createServiceDependencies(t)
	sut := NewService(
		containerClient,
		db,
		mockConvertImage,
		nil,
		reportinsights.NewReportInsightsMock(),
	)
	CreateExamAndReport(t, db, reportId, examUuid, acctId)

	for _, testCase := range testCases {
		t.Run(fmt.Sprintf("test with %d images", testCase.numImages), func(t *testing.T) {

			addImagesToSeries(t, db, examUuid, seriesUid, testCase.numImages, "1/0/0/0/1/0")
			// Note: selectImages still uses reportId internally to get examUuid, but this is an implementation detail
			// The public API (GetOrganVisualizationByExamID) works with examId and calls this internally for each report
			images, err := sut.selectImages(
				context.Background(),
				reportId,
				acctId,
				ELIGIBLE_FOR_MEDSAM,
			)

			if testCase.expectError {
				assert.Error(t, err)
			} else {
				require.NoError(t, err)
				assert.ElementsMatch(t, testCase.expectedImagesChoses, images)
			}
		})
	}
}

func TestFetchVisualizations(t *testing.T) {
	// This test verifies the internal fetchVisualizations method which is used by
	// GetOrganVisualizationByExamID endpoint. The reportId parameter is still needed
	// for the external reportinsights API call, but the public API works with examId.
	rpServer := reportprocessor.NewTestServer([]byte{})
	t.Cleanup(func() {
		rpServer.Close()
	})

	containerClient, db, mockConvertImage := createServiceDependencies(t)
	sut := NewService(
		containerClient,
		db,
		mockConvertImage,
		nil,
		reportinsights.NewReportInsightsMock(),
	)

	reportId := "organviz-exists"
	examId := "organviz-examId"

	testCases := map[string]struct {
		eligibility ExamEligibility
		expectError bool
		expectRes   string
	}{
		"medsam": {
			eligibility: ELIGIBLE_FOR_MEDSAM,
			expectError: false,
			expectRes:   `{"organs":[{"segmentation":"some segments", "body_part": "spleen", "status": "COMPLETED"}]}`,
		},
		"xray": {
			eligibility: ELIGIBLE_FOR_XRAY,
			expectError: false,
			expectRes:   `{"organs":[{"segmentation":"some segments", "body_part": "left lung", "status": "COMPLETED"}]}`,
		},
		"mri": {
			eligibility: ELIGIBLE_FOR_MRI,
			expectError: false,
			expectRes:   `{"organs":[{"segmentation":"some segments", "body_part": "right lung", "status": "COMPLETED"}]}`,
		},
		"unknown": {
			eligibility: UNKNOWN,
			expectError: true,
		},
	}

	for name, testcase := range testCases {
		t.Run(name, func(t *testing.T) {
			res, err := sut.fetchVisualizations(
				context.Background(),
				reportId,
				testcase.eligibility,
				examId,
			)
			if testcase.expectError {
				assert.Error(t, err)
			} else {
				require.NoError(t, err)
				assert.JSONEq(t,
					testcase.expectRes,
					string(res),
				)
			}
		})
	}
}

func createServiceDependencies(
	t *testing.T,
) (*azureUtils.ContainerClient, *sql.DB, ConvertImageFunc) {
	cfg := testutils.LoadTestConfigFromEnvVar(t)
	riContainerUrl, err := azstorageauth.GetContainerClient(
		context.Background(),
		cfg.AzureStorageAccount,
		cfg.ReportInsightsStorageContainer,
		&policy.RetryOptions{
			TryTimeout: 5 * time.Second,
		},
	)
	require.NoError(t, err)
	containerClient := &azureUtils.ContainerClient{
		ReportInsight: riContainerUrl,
	}

	db := testutils.SetupTestDB(t)
	t.Cleanup(func() {
		db.Close()
	})

	mockConvertImage := func(ctx context.Context, imageId string, CiAccept string, CiAuth bool, containerClient azureUtils.ContainerClient, sqldb *sql.DB, waitGroup *sync.WaitGroup, useVoiWindow bool) (io.ReadCloser, int64, error) {
		data := []byte("image12345")
		return io.NopCloser(bytes.NewBuffer(data)), int64(len(data)), nil
	}

	return containerClient, db, mockConvertImage
}
